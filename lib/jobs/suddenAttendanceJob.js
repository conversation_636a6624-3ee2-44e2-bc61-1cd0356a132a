/**
 * Job xử lý tự động cho hệ thống chấm công đột xuất
 * - Cậ<PERSON> nhật trạng thái phiên chấm công
 * - G<PERSON><PERSON> thông báo nhắc nhở
 * - Cập nhật trạng thái vắng mặt
 */

const cron = require('node-cron');
const SuddenAttendanceSession = require('../models/suddenAttendanceSession');
const SuddenAttendanceRecord = require('../models/suddenAttendanceRecord');
const User = require('../models/user');
const PushNotifyManager = require('./pushNotify');
const CONSTANTS = require('../const');
const SavedNotificationModel = require('../models/savedNotification');

class SuddenAttendanceJob {
  constructor() {
    this.isRunning = false;
  }

  /**
   * Lấy danh sách users hiện tại từ targetUnits
   * @param {Array} targetUnits - <PERSON><PERSON> sách ID các tổ
   * @returns {Array} Danh sách ID users
   */
  async getCurrentUsersFromTargetUnits(targetUnits) {
    try {
      if (!targetUnits || targetUnits.length === 0) {
        // Nếu không có targetUnits, lấy tất cả users active
        const users = await User.find({ status: 1 }).select('_id').lean();
        return users.map(u => u._id);
      }

      // Lấy users thuộc các tổ được chỉ định
      const users = await User.find({
        units: { $in: targetUnits },
        status: 1
      }).select('_id').lean();

      return users.map(u => u._id);
    } catch (error) {
      console.error('Error getting current users from target units:', error);
      return [];
    }
  }

  /**
   * Khởi tạo các cron jobs
   */
  init() {
    console.log('[SuddenAttendanceJob] Initializing sudden attendance jobs...');

    // Chạy mỗi phút để kiểm tra và cập nhật trạng thái + gửi thông báo
    cron.schedule('* * * * *', () => {
      this.processSessionUpdates();
    });

    console.log('[SuddenAttendanceJob] Sudden attendance jobs initialized');
  }

  /**
   * Xử lý cập nhật trạng thái phiên chấm công và gửi thông báo
   */
  async processSessionUpdates() {
    if (this.isRunning) return;

    try {
      this.isRunning = true;
      const now = Date.now();

      // 1. Gửi thông báo bắt đầu chấm công (tại startTime)
      await this.sendStartNotifications(now);

      // 2. Gửi thông báo sắp hết thời gian (5 phút trước validEndTime)
      await this.sendAlmostEndNotifications(now);

      // 3. Gửi thông báo hết thời gian (tại validEndTime)
      await this.sendEndNotifications(now);

      // 4. Kích hoạt các phiên đã đến giờ bắt đầu
      await this.activateSessions(now);

      // 5. Hoàn thành các phiên đã hết thời gian chấm công muộn
      await this.completeSessions(now);

      // 6. Cập nhật trạng thái vắng mặt cho những người chưa chấm công
      await this.updateAbsentStatus(now);

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error in processSessionUpdates:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Kích hoạt các phiên chấm công đã đến giờ bắt đầu
   */
  async activateSessions(now) {
    try {
      const sessionsToActivate = await SuddenAttendanceSession.find({
        status: 'scheduled',
        startTime: { $lte: now }
      });

      for (const session of sessionsToActivate) {
        await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
          status: 'active'
        });

        console.log(`[SuddenAttendanceJob] Activated session: ${session.title} (${session._id})`);
      }

      if (sessionsToActivate.length > 0) {
        console.log(`[SuddenAttendanceJob] Activated ${sessionsToActivate.length} sessions`);
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error activating sessions:', error);
    }
  }

  /**
   * Hoàn thành các phiên đã hết thời gian chấm công
   */
  async completeSessions(now) {
    try {
      const sessionsToComplete = await SuddenAttendanceSession.find({
        status: 'active',
        startTime: { $lte: now - (60 * 60 * 1000) } // 1 giờ sau giờ bắt đầu
      });

      for (const session of sessionsToComplete) {
        await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
          status: 'completed'
        });

        console.log(`[SuddenAttendanceJob] Completed session: ${session.title} (${session._id})`);
      }

      if (sessionsToComplete.length > 0) {
        console.log(`[SuddenAttendanceJob] Completed ${sessionsToComplete.length} sessions`);
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error completing sessions:', error);
    }
  }

  /**
   * Cập nhật trạng thái vắng mặt cho những người chưa chấm công
   */
  async updateAbsentStatus(now) {
    try {
      // Tìm các phiên đã hết thời gian chấm công (1 giờ sau giờ bắt đầu)
      const expiredSessions = await SuddenAttendanceSession.find({
        status: { $in: ['active', 'completed'] },
        startTime: { $lte: now - (60 * 60 * 1000) }
      });

      for (const session of expiredSessions) {
        // Lấy danh sách người đã chấm công
        const checkedInUsers = await SuddenAttendanceRecord.find({
          session: session._id
        }).distinct('user');

        // Lấy danh sách users hiện tại từ targetUnits
        const currentTargetUsers = await this.getCurrentUsersFromTargetUnits(session.targetUnits);

        // Tìm những người chưa chấm công
        const absentUsers = currentTargetUsers.filter(
          userId => !checkedInUsers.some(checkedUserId =>
            checkedUserId.toString() === userId.toString()
          )
        );

        // Tạo bản ghi vắng mặt cho những người chưa chấm công
        for (const userId of absentUsers) {
          const existingRecord = await SuddenAttendanceRecord.findOne({
            session: session._id,
            user: userId
          });

          if (!existingRecord) {
            await SuddenAttendanceRecord.create({
              session: session._id,
              user: userId,
              checkinTime: session.startTime + (60 * 60 * 1000), // Thời gian hết hạn
              status: CONSTANTS.SUDDEN_ATTENDANCE.CHECKIN_STATUS.ABSENT,
              note: ''
            });
          }
        }

        // Cập nhật thống kê phiên
        if (absentUsers.length > 0) {
          await SuddenAttendanceSession.findByIdAndUpdate(session._id, {
            $inc: { 'statistics.absentCount': absentUsers.length }
          });

          console.log(`[SuddenAttendanceJob] Updated ${absentUsers.length} absent records for session: ${session.title}`);
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error updating absent status:', error);
    }
  }

  /**
   * Gửi thông báo bắt đầu chấm công (tại startTime)
   * Gửi cho tất cả người trong targetUnits
   */
  async sendStartNotifications(now) {
    try {
      // Tìm các phiên vừa bắt đầu (trong vòng 1 phút qua)
      const startingSessions = await SuddenAttendanceSession.find({
        status: { $in: ['scheduled', 'active'] },
        startTime: {
          $gte: now - (60 * 1000), // 1 phút trước
          $lte: now + (10 * 1000)  // 10 giây sau (tolerance)
        },
        // Chỉ gửi trong khoảng thời gian chính xác
      });

      for (const session of startingSessions) {
        // Lấy danh sách users từ targetUnits
        const targetUsers = await this.getCurrentUsersFromTargetUnits(session.targetUnits);

        if (targetUsers.length > 0) {
          const validEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
          const endTimeText = new Date(validEndTime).toLocaleTimeString('vi-VN');

          const title = 'Chấm công đột xuất bắt đầu';
          const message = `Phiên chấm công đột xuất "${session.title}" đã bắt đầu. Thời gian chấm công đúng giờ sẽ kết thúc lúc ${endTimeText}. Vui lòng chấm công ngay!`;

          const notificationData = {
            link: 'MainContainer',
            extras: {
              tabIndex: 2
            },
            linkWeb: '/attendance'
          };

          let savedNotificationDoc;
          if (session.savedNotification) {
            // Nếu đã có, update nội dung và users
            savedNotificationDoc = await SavedNotificationModel.findByIdAndUpdate(
              session.savedNotification,
              {
                title,
                description: message,
                users: targetUsers,
                type: 'user',
                data: notificationData,
                updatedAt: new Date()
              },
              { new: true }
            );
          } else {
            // Nếu chưa có, tạo mới
            savedNotificationDoc = await SavedNotificationModel.create({
              title,
              description: message,
              users: targetUsers,
              type: 'user',
              data: notificationData,
              createdAt: new Date(),
              updatedAt: new Date()
            });
            session.savedNotification = savedNotificationDoc._id;
            await session.save();
          }

          let successCount = 0;
          let failCount = 0;

          for (const userId of targetUsers) {
            try {
              await PushNotifyManager.sendToMember(
                userId,
                title,
                message,
                notificationData,
                '', // sudden_attendance_start
                'ioc'
              );
              successCount++;
            } catch (error) {
              failCount++;
              console.error(`[SuddenAttendanceJob] Failed to send start notification to user ${userId}:`, error.message);
            }
          }

          // Log để tracking (không cần lưu vào DB vì có logic thời gian)

          console.log(`[SuddenAttendanceJob] Sent start notifications for session "${session.title}": ${successCount} success, ${failCount} failed`);
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error sending start notifications:', error);
    }
  }

  /**
   * Gửi thông báo sắp hết thời gian chấm công đúng giờ (5 phút trước validEndTime)
   * Chỉ gửi cho những người chưa chấm công
   */
  async sendAlmostEndNotifications(now) {
    try {
      // Tìm các phiên sắp hết thời gian chấm công đúng giờ (5 phút nữa)
      const almostEndingSessions = await SuddenAttendanceSession.find({
        status: 'active'
      });

      for (const session of almostEndingSessions) {
        const validEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
        const timeUntilEnd = validEndTime - now;

        // Kiểm tra có phải còn 5 phút không (±30s tolerance)
        if (Math.abs(timeUntilEnd - 5 * 60 * 1000) < 30 * 1000) {
          // Lấy danh sách người chưa chấm công
          const checkedInUsers = await SuddenAttendanceRecord.find({
            session: session._id
          }).distinct('user');

          const targetUsers = await this.getCurrentUsersFromTargetUnits(session.targetUnits);
          const uncheckedUsers = targetUsers.filter(
            userId => !checkedInUsers.some(checkedUserId =>
              checkedUserId.toString() === userId.toString()
            )
          );

          if (uncheckedUsers.length > 0) {
            const endTimeText = new Date(validEndTime).toLocaleTimeString('vi-VN');
            const title = 'Sắp hết thời gian chấm công';
            const message = `Bạn chưa chấm công cho phiên "${session.title}". Thời gian chấm công đúng giờ sẽ kết thúc lúc ${endTimeText}. Vui lòng chấm công ngay!`;

            const notificationData = {
              link: 'MainContainer',
              extras: {
                tabIndex: 2
              },
              linkWeb: '/attendance'
            };

            let savedNotificationDoc;
            if (session.savedNotification) {
              // Nếu đã có, update nội dung và users
              savedNotificationDoc = await SavedNotificationModel.findByIdAndUpdate(
                session.savedNotification,
                {
                  title,
                  description: message,
                  users: uncheckedUsers,
                  type: 'user',
                  data: notificationData,
                  updatedAt: new Date()
                },
                { new: true }
              );
            } else {
              // Nếu chưa có, tạo mới
              savedNotificationDoc = await SavedNotificationModel.create({
                title,
                description: message,
                users: uncheckedUsers,
                type: 'user',
                data: notificationData,
                createdAt: new Date(),
                updatedAt: new Date()
              });
              session.savedNotification = savedNotificationDoc._id;
              await session.save();
            }

            let successCount = 0;
            let failCount = 0;

            for (const userId of uncheckedUsers) {
              try {
                await PushNotifyManager.sendToMember(
                  userId,
                  title,
                  message,
                  notificationData,
                  '', // sudden_attendance_almost_end
                  'ioc'
                );
                successCount++;
              } catch (error) {
                failCount++;
                console.error(`[SuddenAttendanceJob] Failed to send almost-end notification to user ${userId}:`, error.message);
              }
            }

            // Log để tracking (logic thời gian đã tránh duplicate)

            console.log(`[SuddenAttendanceJob] Sent almost-end notifications for session "${session.title}": ${successCount} success, ${failCount} failed`);
          }
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error sending almost-end notifications:', error);
    }
  }

  /**
   * Gửi thông báo hết thời gian chấm công đúng giờ (tại validEndTime)
   * Chỉ gửi cho những người chưa chấm công
   */
  async sendEndNotifications(now) {
    try {
      // Tìm các phiên vừa hết thời gian chấm công đúng giờ
      const endingSessions = await SuddenAttendanceSession.find({
        status: { $in: ['scheduled', 'active'] }
      });

      for (const session of endingSessions) {
        const validEndTime = session.startTime + (session.validDurationMinutes * 60 * 1000);
        const timeSinceEnd = now - validEndTime;

        // Kiểm tra có phải vừa hết thời gian không (trong vòng 1 phút qua)
        if (timeSinceEnd >= 0 && timeSinceEnd <= 60 * 1000) {
          // Lấy danh sách người chưa chấm công
          const checkedInUsers = await SuddenAttendanceRecord.find({
            session: session._id
          }).distinct('user');

          const targetUsers = await this.getCurrentUsersFromTargetUnits(session.targetUnits);
          const uncheckedUsers = targetUsers.filter(
            userId => !checkedInUsers.some(checkedUserId =>
              checkedUserId.toString() === userId.toString()
            )
          );

          if (uncheckedUsers.length > 0) {
            const title = 'Hết thời gian chấm công đúng giờ';
            const message = `Bạn chưa chấm công cho phiên "${session.title}". Thời gian chấm công đúng giờ đã kết thúc. Bạn vẫn có thể chấm công muộn.`;

            const notificationData = {
              link: 'MainContainer',
              extras: {
                tabIndex: 2
              },
              linkWeb: '/attendance'
            };

            let savedNotificationDoc;
            if (session.savedNotification) {
              // Nếu đã có, update nội dung và users
              savedNotificationDoc = await SavedNotificationModel.findByIdAndUpdate(
                session.savedNotification,
                {
                  title,
                  description: message,
                  users: uncheckedUsers,
                  type: 'user',
                  data: notificationData,
                  updatedAt: new Date()
                },
                { new: true }
              );
            } else {
              // Nếu chưa có, tạo mới
              savedNotificationDoc = await SavedNotificationModel.create({
                title,
                description: message,
                users: uncheckedUsers,
                type: 'user',
                data: notificationData,
                createdAt: new Date(),
                updatedAt: new Date()
              });
              session.savedNotification = savedNotificationDoc._id;
              await session.save();
            }

            let successCount = 0;
            let failCount = 0;

            for (const userId of uncheckedUsers) {
              try {
                await PushNotifyManager.sendToMember(
                  userId,
                  title,
                  message,
                  notificationData,
                  '', // sudden_attendance_end
                  'ioc'
                );
                successCount++;
              } catch (error) {
                failCount++;
                console.error(`[SuddenAttendanceJob] Failed to send end notification to user ${userId}:`, error.message);
              }
            }

            // Log để tracking (logic thời gian đã tránh duplicate)

            console.log(`[SuddenAttendanceJob] Sent end notifications for session "${session.title}": ${successCount} success, ${failCount} failed`);
          }
        }
      }

    } catch (error) {
      console.error('[SuddenAttendanceJob] Error sending end notifications:', error);
    }
  }

  /**
   * Dừng tất cả jobs
   */
  stop() {
    console.log('[SuddenAttendanceJob] Stopping sudden attendance jobs...');
    this.isRunning = false;
    // Cron jobs sẽ tự động dừng khi process kết thúc
  }
}

module.exports = new SuddenAttendanceJob();
