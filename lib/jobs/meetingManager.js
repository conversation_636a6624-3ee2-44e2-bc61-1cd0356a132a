const _ = require('lodash');
const CONSTANTS = require('../const');
const moment = require('moment');
const ms = require('ms');
const async = require('async')
const axios = require('axios');
const rp = require('request-promise');
const PushNotifyManager = require('../jobs/pushNotify');
const MeetingSchedule = require('../models/meetingSchedule');
const UserModel = require('../models/user');
const SavedNotificationModel = require('../models/savedNotification');

class MeetingManager {
  constructor() {
    this.reminderIntervals = [
      { time: 60 * 60 * 1000, label: '1 giờ' },       // 1 hour before
      { time: 15 * 60 * 1000, label: '15 phút' }      // 15 minutes before
    ];
  }

  /**
   * G<PERSON><PERSON> thông báo nhắc lịch họp cho cán bộ
   * @param {String} meetingId - ID của cuộc họp
   * @param {Number} reminderTime - Th<PERSON><PERSON> gian nh<PERSON> trước (milliseconds)
   */
  async sendMeetingReminder(meetingId, reminderTime) {
    try {
      const meeting = await MeetingSchedule.findById(meetingId)
        .populate('officers', 'name')
        .populate('assignedBy', 'name');

      if (!meeting || meeting.status !== 1) {
        console.log(`Meeting ${meetingId} not found or inactive`);
        return;
      }

      const now = Date.now();
      const timeUntilMeeting = meeting.startTime - now;
      // Kiểm tra xem có phải thời điểm nhắc nhở không
      if (Math.abs(timeUntilMeeting - reminderTime) > 5 * 60 * 1000) { // tolerance 5 minutes
        return;
      }

      // Xác định loại thông báo
      const reminderType = reminderTime === 60 * 60 * 1000 ? '1hour' : '15minutes';
      // Kiểm tra xem đã gửi thông báo loại này chưa
      const alreadySent = meeting.sentReminders && meeting.sentReminders.some(
        reminder => reminder.type === reminderType
      );
      if (alreadySent) {
        console.log(`${reminderType} reminder already sent for meeting ${meetingId}`);
        return;
      }

      const meetingTimeFormatted = moment(meeting.startTime).format('HH:mm');
      // Tính thời gian chính xác còn lại đến cuộc họp
      const minutesUntilMeeting = Math.round(timeUntilMeeting / (60 * 1000));
      const timeLabel = minutesUntilMeeting > 60 
        ? `${Math.round(minutesUntilMeeting / 60)} giờ ${minutesUntilMeeting % 60} phút`
        : `${minutesUntilMeeting} phút`;

      const title = `Nhắc nhở Lịch họp`;
      const description = `${timeLabel} nữa Cán bộ có Cuộc họp "${meeting.topic}" bắt đầu vào ${meetingTimeFormatted}. Ấn để xem chi tiết lịch họp.`;

      const notificationData = {
        link: 'MainContainer',
        extras: {
          tabIndex: 1
        },
        linkWeb: '/my-work-schedule'
      };

      // Lấy thêm officers từ các tổ liên quan
      let unitOfficers = [];
      if (meeting.units && meeting.units.length > 0) {
        unitOfficers = await UserModel.find({ units: { $in: meeting.units } }, '_id name');
      }

      // Gộp officers từ meeting.officers và unitOfficers, lấy unique theo _id
      const allOfficers = _.uniqBy(
        [...(meeting.officers || []), ...(unitOfficers || [])],
        officer => officer._id.toString()
      );
      const userIds = allOfficers.map(o => o._id);

      let savedNotificationDoc;
      if (meeting.savedNotification) {
        // Nếu đã có, update nội dung và users
        savedNotificationDoc = await SavedNotificationModel.findByIdAndUpdate(
          meeting.savedNotification,
          {
            title,
            description,
            users: userIds,
            type: 'user',
            data: notificationData,
            updatedAt: new Date()
          },
          { new: true }
        );
      } else {
        // Nếu chưa có, tạo mới
        savedNotificationDoc = await SavedNotificationModel.create({
          title,
          description,
          users: userIds,
          type: 'user',
          data: notificationData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        meeting.savedNotification = savedNotificationDoc._id;
        await meeting.save();
      }

      // Gửi thông báo cho từng cán bộ
      let data = []
      for (const officer of allOfficers) {
        try {
          await PushNotifyManager.sendToMember(
            officer._id,
            title,
            description,
            notificationData,
            'job_update',
            'ioc'
          );
          data.push(`Sent meeting reminder to ${officer.name} for meeting: ${meeting.topic}`)
        } catch (error) {
          data.push(`Failed to send reminder to ${officer.name}: `+ JSON.stringify(error));
        }
      }

      // Lưu lại thông tin thông báo đã gửi
      await MeetingSchedule.findByIdAndUpdate(meetingId, {
        $push: {
          sentReminders: {
            type: reminderType,
            data,
            sentAt: now
          }
        }
      });

      console.log(`Saved ${reminderType} reminder record for meeting ${meetingId}`);

    } catch (error) {
      console.error('Error sending meeting reminder:', error);
    }
  }

  /**
   * Gửi thông báo ngay lập tức cho cuộc họp mới tạo
   * @param {String} meetingId - ID của cuộc họp
   */
  async sendImmediateNotification(meetingId) {
    try {
      const meeting = await MeetingSchedule.findById(meetingId)
        .populate('officers', 'name')
        .populate('assignedBy', 'name');

      if (!meeting || meeting.status !== 1) {
        console.log(`Meeting ${meetingId} not found or inactive`);
        return;
      }

      const now = Date.now();
      const timeUntilMeeting = meeting.startTime - now;
      
      // Chỉ gửi thông báo ngay lập tức nếu cuộc họp bắt đầu trong vòng 2 giờ
      if (timeUntilMeeting > 2 * 60 * 60 * 1000) {
        console.log(`Meeting ${meetingId} is too far away for immediate notification`);
        return;
      }

      const reminderType = 'immediate';
      // Kiểm tra xem đã gửi thông báo ngay lập tức chưa
      const alreadySent = meeting.sentReminders && meeting.sentReminders.some(
        reminder => reminder.type === reminderType
      );
      if (alreadySent) {
        console.log(`Immediate notification already sent for meeting ${meetingId}`);
        return;
      }

      const meetingTimeFormatted = moment(meeting.startTime).format('HH:mm DD/MM/YYYY');
      const minutesUntilMeeting = Math.round(timeUntilMeeting / (60 * 1000));
      
      let timeLabel;
      if (minutesUntilMeeting < 1) {
        timeLabel = 'ngay bây giờ';
      } else if (minutesUntilMeeting < 60) {
        timeLabel = `${minutesUntilMeeting} phút nữa`;
      } else {
        const hours = Math.floor(minutesUntilMeeting / 60);
        const minutes = minutesUntilMeeting % 60;
        timeLabel = minutes > 0 ? `${hours} giờ ${minutes} phút nữa` : `${hours} giờ nữa`;
      }

      const title = `Thông báo Lịch họp`;
      const description = `Cuộc họp "${meeting.topic}" sẽ bắt đầu ${timeLabel} (${meetingTimeFormatted}). Ấn để xem chi tiết lịch họp.`;

      const notificationData = {
        link: 'MainContainer',
        extras: {
          tabIndex: 1
        },
        linkWeb: '/my-work-schedule'
      };

      // Lấy thêm officers từ các tổ liên quan
      let unitOfficers = [];
      if (meeting.units && meeting.units.length > 0) {
        unitOfficers = await UserModel.find({ units: { $in: meeting.units } }, '_id name');
      }

      // Gộp officers từ meeting.officers và unitOfficers, lấy unique theo _id
      const allOfficers = _.uniqBy(
        [...(meeting.officers || []), ...(unitOfficers || [])],
        officer => officer._id.toString()
      );
      const userIds = allOfficers.map(o => o._id);

      // Tạo saved notification
      const savedNotificationDoc = await SavedNotificationModel.create({
        title,
        description,
        users: userIds,
        type: 'user',
        data: notificationData,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      if (!meeting.savedNotification) {
        meeting.savedNotification = savedNotificationDoc._id;
        await meeting.save();
      }

      // Gửi thông báo cho từng cán bộ
      let data = []
      for (const officer of allOfficers) {
        try {
          await PushNotifyManager.sendToMember(
            officer._id,
            title,
            description,
            notificationData,
            'job_update',
            'ioc'
          );
          data.push(`Sent immediate meeting notification to ${officer.name} for meeting: ${meeting.topic}`)
        } catch (error) {
          data.push(`Failed to send immediate notification to ${officer.name}: `+ JSON.stringify(error));
        }
      }

      // Lưu lại thông tin thông báo đã gửi
      await MeetingSchedule.findByIdAndUpdate(meetingId, {
        $push: {
          sentReminders: {
            type: reminderType,
            data,
            sentAt: now
          }
        }
      });

      console.log(`Sent immediate notification for meeting ${meetingId}`);

    } catch (error) {
      console.error('Error sending immediate meeting notification:', error);
    }
  }

  /**
   * Tìm và gửi tất cả thông báo nhắc lịch họp cần thiết
   */
  async processUpcomingMeetings() {
    try {
      const now = Date.now();
      
      // Tìm các cuộc họp sắp tới trong 2 giờ tới
      const upcomingMeetings = await MeetingSchedule.find({
        startTime: {
          $gte: now,
          $lte: now + (2 * 60 * 60 * 1000) // 2 hours from now
        },
        status: 1
      }).populate('officers', 'name');

      console.log(`Found ${upcomingMeetings.length} upcoming meetings`);

      for (const meeting of upcomingMeetings) {
        const timeUntilMeeting = meeting.startTime - now;
        
        // Đối với các cuộc họp bắt đầu trong vòng 15 phút và chưa gửi thông báo ngay lập tức
        if (timeUntilMeeting <= 15 * 60 * 1000) {
          const hasImmediateNotification = meeting.sentReminders && meeting.sentReminders.some(
            reminder => reminder.type === 'immediate'
          );
          
          if (!hasImmediateNotification) {
            await this.sendImmediateNotification(meeting._id);
            continue; // Bỏ qua các thông báo thường cho cuộc họp này
          }
        }

        // Xử lý thông báo thường (1 giờ và 15 phút trước)
        for (const interval of this.reminderIntervals) {
          const reminderTime = meeting.startTime - interval.time;
          // Nếu thời gian nhắc nhở đã qua hoặc sắp tới (trong vòng 5 phút)
          if (reminderTime <= now + (5 * 60 * 1000) && reminderTime >= now - (5 * 60 * 1000)) {
            await this.sendMeetingReminder(meeting._id, interval.time);
          }
        }
      }

    } catch (error) {   
      console.error('Error processing upcoming meetings:', error);
    }
  }

  /**
   * Kiểm tra và gửi thông báo cho cuộc họp mới tạo
   * Gọi phương thức này sau khi tạo cuộc họp mới
   * @param {String} meetingId - ID của cuộc họp vừa tạo
   */
  async handleNewMeeting(meetingId) {
    try {
      const meeting = await MeetingSchedule.findById(meetingId);
      if (!meeting || meeting.status !== 1) {
        return;
      }

      const now = Date.now();
      const timeUntilMeeting = meeting.startTime - now;
      
      // Nếu cuộc họp bắt đầu trong vòng 15 phút, gửi thông báo ngay lập tức
      if (timeUntilMeeting <= 15 * 60 * 1000 && timeUntilMeeting > 0) {
        console.log(`New meeting ${meetingId} starts in ${Math.round(timeUntilMeeting / (60 * 1000))} minutes - sending immediate notification`);
        await this.sendImmediateNotification(meetingId);
      } else {
        console.log(`New meeting ${meetingId} scheduled for ${new Date(meeting.startTime)} - will be handled by regular reminder job`);
      }
    } catch (error) {
      console.error('Error handling new meeting:', error);
    }
  }

  /**
   * Lấy label mô tả thời gian nhắc nhở
   * @param {Number} reminderTime - Thời gian nhắc (milliseconds)
   */
  getReminderLabel(reminderTime) {
    const interval = this.reminderIntervals.find(i => i.time === reminderTime);
    console.log(`Getting label for reminder time: ${reminderTime}ms`);
    return interval ? interval.label : `${Math.floor(reminderTime / (60 * 1000))} phút`;
  }

  /**
   * Khởi tạo job định kỳ để kiểm tra và gửi thông báo
   */
  startReminderJob() {
    // Chạy mỗi 5 phút để kiểm tra lịch họp
    this.processUpcomingMeetings();
    setInterval(() => {
      this.processUpcomingMeetings();
    }, 5 * 60 * 1000); // 5 minutes

    console.log('Meeting reminder job started - checking every 5 minutes');
  }
}

module.exports = new MeetingManager;
