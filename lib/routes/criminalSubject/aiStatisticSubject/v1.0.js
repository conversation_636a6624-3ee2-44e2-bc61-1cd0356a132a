const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');
const UnitModel = require('../../../models/unit');
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    unit = '',
    unitIds = [],
    data = {},
    type = 'week', // '3days', '7days', 'week', 'month', 'year'
    endTime = null,
    startTime = null
  } = req.body;

  let statisticsData = {
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const options = ['3days', '7days', 'week', 'month', 'year'];
    if (type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,    
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số data là bắt buộc và phải chứa dữ liệu thống kê tình trạng pháp lý'
        }
      });
    }


    // Tính toán startTime dựa theo endTime và type
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        strategyText = '3 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        strategyText = '7 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        strategyText = 'tuần hiện tại';
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        strategyText = 'của các tuần trong tháng hiện tại';
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        strategyText = 'của các tháng trong năm nay';
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    // Tạo timeText từ startTime và endTime
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    };
    
    timeText = `Từ ${formatDate(startTime)} đến ${formatDate(endDate)}`;
    
    next();
  };
  const getStatisticsData = (next) => {
    // Chuẩn hóa dữ liệu thống kê đối tượng quản lý sang tiếng Việt
    if (data && typeof data === 'object') {
      // 1. Tổng quan thống kê
      const tongQuan = {
        'Tổng số khu vực': data.summary?.totalAreas || 0,
        'Tổng số đối tượng': data.summary?.totalSubjects || 0,
        'Thời gian thống kê': data.timeRange ? {
          'Loại': data.timeRange.type,
          'Từ ngày': new Date(data.timeRange.startTime).toLocaleDateString('vi-VN'),
          'Đến ngày': new Date(data.timeRange.endTime).toLocaleDateString('vi-VN')
        } : {}
      };

      // 2. Thống kê tình trạng pháp lý
      const tinhTrangPhapLy = Array.isArray(data.summary?.legalStatusSummary) ? 
        data.summary.legalStatusSummary
          .filter(item => item.totalCount > 0) // Chỉ lấy những loại có số lượng > 0
          .map(item => ({
            'Loại': data.chartConfig?.labels?.[item.legalStatus] || item.legalStatus,
            'Số lượng': item.totalCount,
            'Tỷ lệ %': data.summary?.totalSubjects > 0 ? 
              Math.round((item.totalCount / data.summary.totalSubjects) * 100) : 0
          })) : [];

      // 3. Thống kê theo loại vi phạm
      const loaiViPham = Array.isArray(data.summary?.categorySummary) ? 
        data.summary.categorySummary
          .filter(item => item.totalCount > 0) // Chỉ lấy những loại có số lượng > 0
          .map(item => ({
            'Loại': data.chartConfig?.categoryLabels?.[item.category] || item.category,
            'Số lượng': item.totalCount,
            'Tỷ lệ %': data.summary?.totalSubjects > 0 ? 
              Math.round((item.totalCount / data.summary.totalSubjects) * 100) : 0
          })) : [];

      // 4. Thống kê theo khu vực
      const theoKhuVuc = Array.isArray(data.areas) ? 
        data.areas
          .filter(area => area.totalCount > 0) // Chỉ lấy khu vực có đối tượng
          .sort((a, b) => b.totalCount - a.totalCount) // Sắp xếp theo số lượng giảm dần
          .map(area => ({
            'Tên khu vực': area.areaName || 'Không xác định',
            'Số lượng đối tượng': area.totalCount,
            'Tỷ lệ %': data.summary?.totalSubjects > 0 ? 
              Math.round((area.totalCount / data.summary.totalSubjects) * 100) : 0
          })) : [];

      // 5. Thống kê theo ngày trong tuần (nếu có)
      const theoNgay = Array.isArray(data.metrics) ? 
        data.metrics
          .filter(metric => metric.totalSubjects > 0) // Chỉ lấy ngày có đối tượng mới
          .map(metric => ({
            'Ngày': metric.timeLabel,
            'Số lượng đối tượng mới': metric.totalSubjects
          })) : [];

      // Tổng hợp dữ liệu cuối cùng
      statisticsData = {
        'Tổng quan': tongQuan,
        'Thống kê tình trạng pháp lý': tinhTrangPhapLy,
        'Thống kê theo loại vi phạm': loaiViPham,
        'Thống kê theo khu vực': theoKhuVuc,
        ...(theoNgay.length > 0 && { 'Thống kê theo ngày': theoNgay })
      };
    }

    next();
  };


  const callAIStream = (next) => {
    const contextPrompt = `

Dữ liệu đầu vào là thống kê số lượng đối tượng mới ${strategyText} ${timeText}, phân loại theo ngày, khu vực, loại vi phạm và tình trạng pháp lý. 
Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

1. **Tổng quan theo thời gian**
   - Tổng số đối tượng mới trong tuần.
   - Phân tích xu hướng theo ngày: ngày nào cao điểm, ngày nào thấp nhất.
   - Nhận định sự phân bố: tăng dần, giảm dần hay dao động.

2. **Theo khu vực**
   - So sánh số lượng đối tượng mới tại từng địa bàn.
   - Xác định địa bàn trọng điểm có số lượng cao nhất.
   - Nhận định sự phân bổ đều/không đều giữa các khu vực.

3. **Theo loại vi phạm**
   - Thống kê số lượng và tỷ lệ % theo các loại: An ninh, Hình sự, Ma túy, Khác.
   - Loại vi phạm nào chiếm tỷ lệ cao nhất, loại nào đáng cảnh báo.

4. **Theo tình trạng pháp lý**
   - Thống kê đối tượng mới thuộc nhóm tiền án, tiền sự, án treo, quản chế, truy nã.
   - Nhận định mức độ phức tạp của các đối tượng mới phát sinh.

5. **Cảnh báo & Đề xuất**
   - Địa bàn hoặc loại vi phạm cần tập trung xử lý.
   - Đối tượng thuộc nhóm pháp lý nào cần giám sát chặt.
   - Đề xuất tăng cường biện pháp nghiệp vụ, phân bổ lực lượng phù hợp.

Trình bày ngắn gọn, rõ ràng, có số liệu minh chứng và gạch đầu dòng
Trả lời hoàn toan bằng tiếng Việt.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích kỷ luật điểm danh cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  console.log('Delta received:', delta);
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          console.log('Full AI Response:', fullContent);
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          unitIds,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};
