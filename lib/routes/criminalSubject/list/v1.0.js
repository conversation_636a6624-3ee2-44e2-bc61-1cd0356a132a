const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const CriminalSubjectModel = require('../../../models/criminalSubject');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const { change_alias } = require('../../../util/tool');

/**
 * API lấy danh sách đối tượng hình sự với tìm kiếm và filter
 * POST /api/v1.0/criminal-subject/list
 */
module.exports = (req, res) => {
  const {
    page = 1,
    limit = 20,
    textSearch,
    category,
    dangerLevel,
    legalStatus,
    area,
    managingUnit,
    assignedOfficer,
    createdFrom,
    createdTo,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.body;

  let subjects = [];
  let totalCount = 0;
  let searchQuery = { status: 1, deletedAt: null, legalStatus: { $ne: 'free' } };

  // Validation schema
  const schema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    textSearch: Joi.string().trim().max(100),
    category: Joi.string().valid('security', 'criminal', 'drug', 'other'),
    dangerLevel: Joi.string().valid('low', 'medium', 'high'),
    legalStatus: Joi.string().valid('pretrial', 'trial', 'suspended_sentence', 'detention', 'arrest', 'free'),
    area: Joi.objectId(),
    managingUnit: Joi.objectId(),
    assignedOfficer: Joi.objectId(),
    createdFrom: Joi.number().integer(),
    createdTo: Joi.number().integer(),
    sortBy: Joi.string().valid('name', 'dob', 'createdAt', 'category', 'dangerLevel').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const buildSearchQuery = (next) => {
    // Text search - tìm kiếm theo tên, CCCD, SĐT
    if (textSearch && textSearch.trim()) {
      const searchText = textSearch.trim();
      const nameAlias = change_alias(searchText);

      searchQuery.$or = [
        { nameAlias: new RegExp(nameAlias, 'i') },
        { idNumber: new RegExp(searchText, 'i') },
        { phones: new RegExp(searchText, 'i') }
      ];
    }

    // Category filter
    if (category) {
      searchQuery.category = category;
    }

    // Danger level filter
    if (dangerLevel) {
      searchQuery.dangerLevel = dangerLevel;
    }

    // Legal status filter
    if (legalStatus) {
      searchQuery.legalStatus = legalStatus;
    }

    // Area filter
    if (area) {
      searchQuery.areas = area;
    }

    // Managing unit filter
    if (managingUnit) {
      searchQuery.managingUnit = managingUnit;
    }

    // Assigned officer filter
    if (assignedOfficer) {
      searchQuery.assignedOfficers = assignedOfficer;
    }

    // Date range filter
    if (createdFrom || createdTo) {
      searchQuery.createdAt = {};
      if (createdFrom) searchQuery.createdAt.$gte = createdFrom;
      if (createdTo) searchQuery.createdAt.$lte = createdTo;
    }

    next();
  };

  const getSubjects = (next) => {
    const skip = (page - 1) * limit;
    const sortObj = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    CriminalSubjectModel.find(searchQuery)
      .select('-status -nameAlias -createdBy')
      .populate('managingUnit', 'name')
      .populate({
        path: 'assignedOfficers',
        select: 'name idNumber avatar units positions phones',
        populate: [{
          path: 'units',
          select: 'name'
        }, {
          path: 'positions',
          select: 'name'
        }]
      })
      .populate('areas', 'name level')
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit))
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        subjects = results;
        next();
      });
  };

  const getTotalCount = (next) => {
    CriminalSubjectModel.countDocuments(searchQuery, (err, count) => {
      if (err) {
        return next(err);
      }

      totalCount = count;
      next();
    });
  };

  const formatResponse = (next) => {
    const totalPages = Math.ceil(totalCount / limit);

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        subjects: subjects,
        pagination: {
          currentPage: parseInt(page),
          totalPages: totalPages,
          totalItems: totalCount,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        filters: {
          textSearch,
          category,
          dangerLevel,
          legalStatus,
          area,
          managingUnit,
          assignedOfficer,
          createdFrom,
          createdTo
        },
        sorting: {
          sortBy,
          sortOrder
        }
      }
    });
  };

  const writeLog = (data, next) => {
    next(null, data);

    try {
      SystemLogModel && SystemLogModel.create({
        user: _.get(req, 'user.id', ''),
        action: 'list_criminal_subject',
        description: 'Lấy danh sách đối tượng hình sự',
        data: req.body,
        updatedData: _.get(data, 'data')
      }, () => { });
    } catch (e) { }
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    buildSearchQuery,
    getSubjects,
    getTotalCount,
    formatResponse,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
