const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const CriminalSubjectModel = require('../../../models/criminalSubject');
const AreaModel = require('../../../models/area');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê tình trạng pháp lý (legalStatus) của CriminalSubject theo khu vực
 * POST /api/v1.0/criminal-subject/statistic-summary
 *
 * Trả về thống kê CriminalSubject theo legalStatus được group theo area
 * Đầu vào: thời gian (type, endTime)
 * Đầu ra: Thống kê số lượng CriminalSubject theo legalStatus từng area
 */
module.exports = (req, res) => {
  const userId = req.user.id;

  const {
    type = 'week',
    endTime
  } = req.body;

  let startTime;

  let result;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const options = ['3days','7days','week','month','year'];
    
    if(type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if(!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }
    next();
  };



  const calculateTimeRange = (next) => {
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }
    startTime.setHours(0, 0, 0, 0);

    next();
  };

  const getStatisticByTime = (next) => {
    // Query CriminalSubjectModel để lấy thống kê theo thời gian
    const subjectQuery = {
      createdAt: {
        $gte: startTime.getTime(),
        $lte: new Date(endTime).getTime()
      },
      status: 1 // Chỉ lấy những record active
    };
    
    // Tạo pipeline aggregate dựa vào type để phân chia thời gian
    let dateGrouping;
    let sortField;
    
    switch (type) {
      case '3days':
      case '7days':
        // Chia theo từng ngày
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          day: { $dayOfMonth: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1, '_id.day': 1 };
        break;
      case 'week':
        // Chia theo từng thứ trong tuần (1=Chủ nhật, 2=Thứ 2, ...)
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          week: { $week: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          dayOfWeek: { $dayOfWeek: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.week': 1, '_id.dayOfWeek': 1 };
        break;
      case 'month':
        // Chia theo từng tuần trong tháng
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          week: { $week: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1, '_id.week': 1 };
        break;
      case 'year':
        // Chia theo từng tháng trong năm
        dateGrouping = {
          year: { $year: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } },
          month: { $month: { $add: [{ $toDate: '$createdAt' }, 7 * 60 * 60 * 1000] } }
        };
        sortField = { '_id.year': 1, '_id.month': 1 };
        break;
    }

    CriminalSubjectModel.aggregate([
      { $match: subjectQuery },
      {
        $group: {
          _id: dateGrouping,
          totalSubjects: { $sum: 1 } // Đếm số CriminalSubject
        }
      },
      { $sort: sortField }
    ])
      .then((subjectStats) => {
        // Helper function để tạo tất cả time periods cho type
        const generateAllTimePeriods = () => {
          const periods = [];
          const start = new Date(startTime);
          const end = new Date(endTime);
          
          switch (type) {
            case '3days':
            case '7days':
              // Tạo tất cả các ngày trong khoảng thời gian
              for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                periods.push({
                  timeLabel: `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}`,
                  period: {
                    year: d.getFullYear(),
                    month: d.getMonth() + 1,
                    day: d.getDate()
                  }
                });
              }
              break;
            case 'week':
              // Tạo tất cả các thứ trong tuần (T2 -> CN)
              const dayNames = ['T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'CN'];
              for (let i = 0; i < 7; i++) {
                periods.push({
                  timeLabel: dayNames[i],
                  period: {
                    dayOfWeek: i === 6 ? 1 : i + 2 // T2=2, T3=3, T4=4, T5=5, T6=6, T7=7, CN=1
                  }
                });
              }
              break;
            case 'month':
              // Tạo tất cả các tuần trong tháng
              const startWeek = Math.floor((start.getTime() - new Date(start.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
              const endWeek = Math.floor((end.getTime() - new Date(end.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1;
              for (let w = startWeek; w <= endWeek; w++) {
                periods.push({
                  timeLabel: `Tuần ${w - startWeek + 1}`,
                  period: {
                    week: w
                  }
                });
              }
              break;
            case 'year':
              // Tạo tất cả các tháng trong năm
              for (let m = start.getMonth() + 1; m <= end.getMonth() + 1; m++) {
                periods.push({
                  timeLabel: `T${m}`,
                  period: {
                    month: m
                  }
                });
              }
              break;
          }
          return periods;
        };

        // Helper function để format label thời gian
        const formatTimeLabel = (timeGroup) => {
          switch (type) {
            case '3days':
            case '7days':
              return `${timeGroup.day.toString().padStart(2, '0')}/${timeGroup.month.toString().padStart(2, '0')}`;
            case 'week':
              const dayNames = ['', 'CN', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7'];
              return dayNames[timeGroup.dayOfWeek];
            case 'month':
              return `Tuần ${timeGroup.week - Math.floor((new Date(timeGroup.year, timeGroup.month - 1, 1).getTime() - new Date(timeGroup.year, 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000)) + 1}`;
            case 'year':
              return `T${timeGroup.month}`;
            default:
              return '';
          }
        };

        // Tạo tất cả time periods
        const allTimePeriods = generateAllTimePeriods();

        // Tạo một object để lưu tổng số đối tượng cho mỗi time period
        const timePeriodsData = {};
        
        // Khởi tạo tất cả time periods với giá trị 0
        allTimePeriods.forEach(period => {
          timePeriodsData[period.timeLabel] = {
            timeLabel: period.timeLabel,
            period: period.period,
            totalSubjects: 0
          };
        });
        
        // Cập nhật dữ liệu thực tế từ subjectStats
        subjectStats.forEach(stat => {
          const timeLabel = formatTimeLabel(stat._id);
          if (timePeriodsData[timeLabel]) {
            timePeriodsData[timeLabel].totalSubjects = stat.totalSubjects;
          }
        });

        // Tính tổng số đối tượng
        const totalSubjects = subjectStats.reduce((sum, stat) => sum + stat.totalSubjects, 0);

        // Convert thành array
        const metrics = Object.values(timePeriodsData);

        // Xử lý format cho trường hợp month
        if(type === 'month') {
          metrics.forEach(item => {
            if(item.period.week) {
              // Tính toán tuần trong tháng
              const weekStart = new Date(startTime.getFullYear(), 0, (item.period.week - 1) * 7 + 1);
              const weekEnd = new Date(startTime.getFullYear(), 0, item.period.week * 7);
              
              // Đảm bảo tuần nằm trong tháng đang xét
              const month = startTime.getMonth() + 1;
              let labelStart = weekStart;
              let labelEnd = weekEnd;
              if (weekStart.getMonth() + 1 < month) labelStart = new Date(startTime.getFullYear(), month - 1, 1);
              if (weekEnd.getMonth() + 1 > month) labelEnd = new Date(startTime.getFullYear(), month, 0);
              item.timeLabel = `${labelStart.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')} - ${labelEnd.getDate().toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}`;
            }
          });
        }

        // Lưu metrics vào biến global để sử dụng trong getStatisticDocument
        result = { metrics, totalSubjectsOverTime: totalSubjects };
        next(null, result);
      })
      .catch((err) => {
        next(err);
      });
  };

  const getStatisticDocument = (timeMetrics, next) => {
    // B1: Lấy tất cả các area có parent: null
    AreaModel.find({ parent: null, status: 1 }, '_id name')
      .lean()
      .then((parentAreas) => {
        if (!parentAreas || parentAreas.length === 0) {
          // Định nghĩa legalStatusConfig để tạo summary rỗng
          const legalStatusConfig = {
            'pretrial': { name: 'Tiền án', color: '#FF9600' },
            'trial': { name: 'Tiền sự', color: '#6750A4' },
            'suspended_sentence': { name: 'Án treo', color: '#33C5E8' },
            'detention': { name: 'Quản chế', color: '#007CFE' },
            'arrest': { name: 'Truy nã', color: '#D30500' }
          };
          
          const legalStatusSummary = Object.keys(legalStatusConfig).map(status => ({
            legalStatus: status,
            totalCount: 0
          }));

          // Định nghĩa categoryConfig để tạo summary rỗng
          const categoryConfig = {
            'security': { name: 'An ninh', color: '#00BF30' },
            'criminal': { name: 'Hình sự', color: '#FFC107' },
            'drug': { name: 'Ma túy', color: '#D30500' },
            'other': { name: 'Khác', color: '#007CFE' }
          };
          
          const categorySummary = Object.keys(categoryConfig).map(cat => ({
            category: cat,
            totalCount: 0
          }));
          
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              title: `Thống kê tình trạng pháp lý theo khu vực`,
              summary: {
                totalAreas: 0,
                totalSubjects: 0,
                legalStatusSummary: legalStatusSummary,
                categorySummary: categorySummary
              },
              chartConfig: {
                colors: {
                  'pretrial': '#FF9600',
                  'trial': '#6750A4',
                  'suspended_sentence': '#33C5E8',
                  'detention': '#007CFE',
                  'arrest': '#D30500'
                },
                labels: {
                  'pretrial': 'Tiền án',
                  'trial': 'Tiền sự',
                  'suspended_sentence': 'Án treo',
                  'detention': 'Quản chế',
                  'arrest': 'Truy nã'
                },
                categoryColors: {
                  'security': '#00BF30',
                  'criminal': '#FFC107',
                  'drug': '#D30500',
                  'other': '#007CFE'
                },
                categoryLabels: {
                  'security': 'An ninh',
                  'criminal': 'Hình sự',
                  'drug': 'Ma túy',
                  'other': 'Khác'
                }
              },
              areas: [],
              metrics: timeMetrics?.metrics || [],
              timeRange: {
                startTime: startTime.getTime(),
                endTime: new Date(endTime).getTime(),
                type
              }
            }
          });
        }

        // B2: Query CriminalSubjectModel để lấy thống kê theo legalStatus
        const subjectQuery = {
          createdAt: {
            $gte: startTime.getTime(),
            $lte: new Date(endTime).getTime()
          },
          status: 1 // Chỉ lấy những record active
        };

        // Query thống kê theo legalStatus
        const legalStatusPromise = CriminalSubjectModel.aggregate([
          { $match: subjectQuery },
          {
            $addFields: {
              // Tạo field để group - nếu areas rỗng hoặc null hoặc phần tử đầu tiên là null thì dùng "no_area"
              groupKey: {
                $cond: {
                  if: { 
                    $or: [
                      { $eq: ['$areas', []] }, 
                      { $eq: ['$areas', null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, ""] }
                    ]
                  },
                  then: "no_area",
                  else: { $arrayElemAt: ['$areas', 0] } // Lấy area đầu tiên
                }
              }
            }
          },
          {
            $group: {
              _id: {
                area: '$groupKey',
                legalStatus: '$legalStatus'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.area',
              legalStatusCounts: {
                $push: {
                  legalStatus: '$_id.legalStatus',
                  count: '$count'
                }
              },
              totalCount: { $sum: '$count' }
            }
          },
          {
            $sort: { totalCount: -1 }
          }
        ]);

        // Query thống kê theo category
        const categoryPromise = CriminalSubjectModel.aggregate([
          { $match: subjectQuery },
          {
            $addFields: {
              // Tạo field để group - nếu areas rỗng hoặc null hoặc phần tử đầu tiên là null thì dùng "no_area"
              groupKey: {
                $cond: {
                  if: { 
                    $or: [
                      { $eq: ['$areas', []] }, 
                      { $eq: ['$areas', null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, ""] }
                    ]
                  },
                  then: "no_area",
                  else: { $arrayElemAt: ['$areas', 0] } // Lấy area đầu tiên
                }
              }
            }
          },
          {
            $group: {
              _id: {
                area: '$groupKey',
                category: '$category'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.area',
              categoryCounts: {
                $push: {
                  category: '$_id.category',
                  count: '$count'
                }
              },
              totalCount: { $sum: '$count' }
            }
          },
          {
            $sort: { totalCount: -1 }
          }
        ]);

        Promise.all([legalStatusPromise, categoryPromise])
          .then(([legalStatusResults, categoryResults]) => {
            // Định nghĩa các legalStatus và màu tương ứng
            const legalStatusConfig = {
              'pretrial': { name: 'Tiền án', color: '#FF9600' },
              'trial': { name: 'Tiền sự', color: '#6750A4' },
              'suspended_sentence': { name: 'Án treo', color: '#33C5E8' },
              'detention': { name: 'Quản chế', color: '#007CFE' },
              'arrest': { name: 'Truy nã', color: '#D30500' }
            };

            // Định nghĩa các category và màu tương ứng
            const categoryConfig = {
              'security': { name: 'An ninh', color: '#00BF30' },
              'criminal': { name: 'Hình sự', color: '#FFC107' },
              'drug': { name: 'Ma túy', color: '#D30500' },
              'other': { name: 'Khác', color: '#007CFE' }
            };

            // Xử lý dữ liệu thống kê theo legalStatus
            const legalStatusStatsMap = {};
            let legalStatusNoAreaStats = null;

            legalStatusResults.forEach(item => {
              const areaKey = item._id;
              
              if (areaKey === "no_area" || areaKey === null) {
                // Xử lý dữ liệu không có khu vực
                legalStatusNoAreaStats = {
                  areaName: "Không có khu vực",
                  totalCount: item.totalCount
                };
              } else {
                // Xử lý dữ liệu có khu vực
                legalStatusStatsMap[areaKey.toString()] = {
                  totalCount: item.totalCount
                };
              }
            });

            // Xử lý dữ liệu thống kê theo category
            const categoryStatsMap = {};
            let categoryNoAreaStats = null;

            categoryResults.forEach(item => {
              const areaKey = item._id;
              
              if (areaKey === "no_area" || areaKey === null) {
                // Xử lý dữ liệu không có khu vực
                categoryNoAreaStats = {
                  areaName: "Không có khu vực",
                  totalCount: item.totalCount
                };
              } else {
                // Xử lý dữ liệu có khu vực
                categoryStatsMap[areaKey.toString()] = {
                  totalCount: item.totalCount
                };
              }
            });

            // Merge areas với thống kê legalStatus
            const legalStatusAreas = parentAreas.map((area) => {
              const areaId = area._id.toString();
              const stats = legalStatusStatsMap[areaId] || { 
                totalCount: 0
              };
              
              return {
                areaId: area._id,
                areaName: area.name,
                totalCount: stats.totalCount
              };
            });

            // Thêm dữ liệu "Không có khu vực" cho legalStatus
            if (!legalStatusNoAreaStats) {
              legalStatusNoAreaStats = {
                areaId: null,
                areaName: "Không có khu vực",
                totalCount: 0
              };
            } else {
              legalStatusNoAreaStats.areaId = null;
            }
            legalStatusAreas.push(legalStatusNoAreaStats);

            // Sort theo totalCount giảm dần
            legalStatusAreas.sort((a, b) => b.totalCount - a.totalCount);

            const totalSubjects = legalStatusAreas.reduce((sum, area) => sum + area.totalCount, 0);

            // Tính tổng theo từng legalStatus
            const legalStatusSummary = Object.keys(legalStatusConfig).map(status => {
              const totalCount = legalStatusResults.reduce((sum, area) => {
                const statusData = area.legalStatusCounts.find(s => s.legalStatus === status);
                return sum + (statusData ? statusData.count : 0);
              }, 0);
              
              return {
                legalStatus: status,
                totalCount: totalCount
              };
            });

            // Tính tổng theo từng category
            const categorySummary = Object.keys(categoryConfig).map(cat => {
              const totalCount = categoryResults.reduce((sum, area) => {
                const catData = area.categoryCounts.find(c => c.category === cat);
                return sum + (catData ? catData.count : 0);
              }, 0);
              
              return {
                category: cat,
                totalCount: totalCount
              };
            });

            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: `Thống kê tình trạng pháp lý`,
                summary: {
                  totalAreas: legalStatusAreas.length,
                  totalSubjects: totalSubjects,
                  legalStatusSummary: legalStatusSummary,
                  categorySummary: categorySummary
                },
                chartConfig: {
                  colors: {
                    'pretrial': '#FF9600',
                    'trial': '#6750A4',
                    'suspended_sentence': '#33C5E8',
                    'detention': '#007CFE',
                    'arrest': '#D30500'
                  },
                  labels: {
                    'pretrial': 'Tiền án',
                    'trial': 'Tiền sự',
                    'suspended_sentence': 'Án treo',
                    'detention': 'Quản chế',
                    'arrest': 'Truy nã'
                  },
                  categoryColors: {
                    'security': '#00BF30',
                    'criminal': '#FFC107',
                    'drug': '#D30500',
                    'other': '#007CFE'
                  },
                  categoryLabels: {
                    'security': 'An ninh',
                    'criminal': 'Hình sự',
                    'drug': 'Ma túy',
                    'other': 'Khác'
                  }
                },
                areas: legalStatusAreas,
                metrics: timeMetrics?.metrics || [],
                timeRange: {
                  startTime: startTime.getTime(),
                  endTime: new Date(endTime).getTime(),
                  type
                }
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    validateParams,
    calculateTimeRange,
    getStatisticByTime,
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
