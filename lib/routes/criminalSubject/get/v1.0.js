const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectModel = require('../../../models/criminalSubject');
const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy thông tin chi tiết đối tượng hình sự
 * POST /api/v1.0/criminal-subject/get
 */
module.exports = (req, res) => {
  const { subjectId } = req.body;

  let subjectInfo;

  // Validation schema
  const schema = Joi.object({
    subjectId: Joi.objectId().required()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getSubjectInfo = (next) => {
    CriminalSubjectModel.findOne({
      _id: subjectId,
      status: 1
    }, '-status -nameAlias -createdBy')
      .populate('managingUnit', 'name')
      .populate({
        path: 'assignedOfficers',
        select: 'name idNumber avatar units positions phones',
        populate: [{
          path: 'units',
          select: 'name'
        }, {
          path: 'positions',
          select: 'name'
        }]
      })
      .populate('areas', 'name level')
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.CRIMINAL_SUBJECT.NOT_FOUND
          });
        }

        subjectInfo = result;
        next();
      });
  };

  const getUpdateHistory = (next) => {
    CriminalSubjectUpdateModel.find({
      subjectId: subjectId,
      status: 1
    }, '-status -updatedBy')
      .sort({ createdAt: -1 })
      .limit(50) // Giới hạn 50 bản cập nhật gần nhất
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: {
            subject: subjectInfo,
            updateHistory: results
          }
        });
      });
  };

  const writeLog = (data, next) => {
    next(null, data);

    try {
      SystemLogModel && SystemLogModel.create({
        user: _.get(req, 'user.id', ''),
        action: 'get_criminal_subject',
        description: 'Lấy chi tiết đối tượng hình sự',
        data: req.body,
        updatedData: _.get(data, 'data')
      }, () => { });
    } catch (e) { }
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    getSubjectInfo,
    getUpdateHistory,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
