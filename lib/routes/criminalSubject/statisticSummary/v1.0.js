const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');
const CriminalSubjectModel = require('../../../models/criminalSubject');
const AreaModel = require('../../../models/area');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API thống kê tình trạng pháp lý (legalStatus) của CriminalSubject theo khu vực
 * POST /api/v1.0/criminal-subject/statistic-summary
 *
 * Trả về thống kê CriminalSubject theo legalStatus được group theo area
 * Đầu vào: không cần tham số thời gian
 * Đầu ra: Thống kê số lượng CriminalSubject theo legalStatus từng area (toàn bộ thời gian)
 */
module.exports = (req, res) => {
  const userId = req.user.id;

  let result;

  /**
   * Lấy dữ liệu thống kê từ database
   */

  const getStatisticDocument = (next) => {
    // B1: Lấy tất cả các area có parent: null
    AreaModel.find({ parent: null, status: 1 }, '_id name')
      .lean()
      .then((parentAreas) => {
        if (!parentAreas || parentAreas.length === 0) {
          // Định nghĩa legalStatusConfig để tạo summary rỗng
          const legalStatusConfig = {
            'pretrial': { name: 'Tiền án', color: '#FF9600' },
            'trial': { name: 'Tiền sự', color: '#6750A4' },
            'suspended_sentence': { name: 'Án treo', color: '#33C5E8' },
            'detention': { name: 'Quản chế', color: '#007CFE' },
            'arrest': { name: 'Truy nã', color: '#D30500' }
          };
          
          // Định nghĩa dangerLevelConfig
          const dangerLevelConfig = {
            'low': { name: 'Ít nghiêm trọng', color: '#4CAF50' },
            'medium': { name: 'Nghiêm trọng', color: '#FF9800' },
            'high': { name: 'Đặc biệt nghiêm trọng', color: '#F44336' }
          };
          
          const legalStatusSummary = Object.keys(legalStatusConfig).map(status => ({
            legalStatus: status,
            totalCount: 0
          }));
          
          const dangerLevelSummary = Object.keys(dangerLevelConfig).map(level => ({
            dangerLevel: level,
            totalCount: 0
          }));
          
          return next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            data: {
              title: `Thống kê tình trạng pháp lý theo khu vực`,
              summary: {
                totalAreas: 0,
                totalSubjects: 0,
                legalStatusSummary: legalStatusSummary
              },
              chartConfig: {
                colors: {
                  'pretrial': '#FF9600',
                  'trial': '#6750A4',
                  'suspended_sentence': '#33C5E8',
                  'detention': '#007CFE',
                  'arrest': '#D30500'
                },
                labels: {
                  'pretrial': 'Tiền án',
                  'trial': 'Tiền sự',
                  'suspended_sentence': 'Án treo',
                  'detention': 'Quản chế',
                  'arrest': 'Truy nã'
                }
              },
              areas: [],
              timeRange: {
                type: 'all_time'
              }
            },
            dangerData: {
              title: 'Thống kê mức độ nghiêm trọng theo khu vực',
              summary: {
                totalAreas: 0,
                totalSubjects: 0,
                dangerLevelSummary: dangerLevelSummary
              },
              chartConfig: {
                colors: {
                  'low': '#4CAF50',
                  'medium': '#FF9800',
                  'high': '#F44336'
                },
                labels: {
                  'low': 'Ít nghiêm trọng',
                  'medium': 'Nghiêm trọng',
                  'high': 'Đặc biệt nghiêm trọng'
                }
              },
              areas: []
            }
          });
        }

        // B2: Query CriminalSubjectModel để lấy thống kê theo legalStatus
        const subjectQuery = {
          status: 1 // Chỉ lấy những record active
        };

        // Query thống kê theo legalStatus
        const legalStatusPromise = CriminalSubjectModel.aggregate([
          { $match: subjectQuery },
          {
            $addFields: {
              // Tạo field để group - nếu areas rỗng hoặc null hoặc phần tử đầu tiên là null thì dùng "no_area"
              groupKey: {
                $cond: {
                  if: { 
                    $or: [
                      { $eq: ['$areas', []] }, 
                      { $eq: ['$areas', null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, ""] }
                    ]
                  },
                  then: "no_area",
                  else: { $arrayElemAt: ['$areas', 0] } // Lấy area đầu tiên
                }
              }
            }
          },
          {
            $group: {
              _id: {
                area: '$groupKey',
                legalStatus: '$legalStatus'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.area',
              legalStatusCounts: {
                $push: {
                  legalStatus: '$_id.legalStatus',
                  count: '$count'
                }
              },
              totalCount: { $sum: '$count' }
            }
          },
          {
            $sort: { totalCount: -1 }
          }
        ]);

        // Query thống kê theo dangerLevel
        const dangerLevelPromise = CriminalSubjectModel.aggregate([
          { $match: subjectQuery },
          {
            $addFields: {
              // Tạo field để group - nếu areas rỗng hoặc null hoặc phần tử đầu tiên là null thì dùng "no_area"
              groupKey: {
                $cond: {
                  if: { 
                    $or: [
                      { $eq: ['$areas', []] }, 
                      { $eq: ['$areas', null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, null] },
                      { $eq: [{ $arrayElemAt: ['$areas', 0] }, ""] }
                    ]
                  },
                  then: "no_area",
                  else: { $arrayElemAt: ['$areas', 0] } // Lấy area đầu tiên
                }
              }
            }
          },
          {
            $group: {
              _id: {
                area: '$groupKey',
                dangerLevel: '$dangerLevel'
              },
              count: { $sum: 1 }
            }
          },
          {
            $group: {
              _id: '$_id.area',
              dangerLevelCounts: {
                $push: {
                  dangerLevel: '$_id.dangerLevel',
                  count: '$count'
                }
              },
              totalCount: { $sum: '$count' }
            }
          },
          {
            $sort: { totalCount: -1 }
          }
        ]);

        Promise.all([legalStatusPromise, dangerLevelPromise])
        Promise.all([legalStatusPromise, dangerLevelPromise])
          .then(([legalStatusResults, dangerLevelResults]) => {
            // Định nghĩa các legalStatus và màu tương ứng
            const legalStatusConfig = {
              'pretrial': { name: 'Tiền án', color: '#FF9600' },
              'trial': { name: 'Tiền sự', color: '#6750A4' },
              'suspended_sentence': { name: 'Án treo', color: '#33C5E8' },
              'detention': { name: 'Quản chế', color: '#007CFE' },
              'arrest': { name: 'Truy nã', color: '#D30500' }
            };

            // Định nghĩa các dangerLevel và màu tương ứng
            const dangerLevelConfig = {
              'low': { name: 'Thấp', color: '#4CAF50' },
              'medium': { name: 'Trung bình', color: '#FF9800' },
              'high': { name: 'Cao', color: '#F44336' }
            };

            // Xử lý dữ liệu thống kê theo legalStatus
            const legalStatusStatsMap = {};
            let legalStatusNoAreaStats = null;

            legalStatusResults.forEach(item => {
              const areaKey = item._id;
              
              if (areaKey === "no_area" || areaKey === null) {
                // Xử lý dữ liệu không có khu vực
                const subjects = Object.keys(legalStatusConfig).map(status => {
                  const statusData = item.legalStatusCounts.find(s => s.legalStatus === status);
                  return {
                    legalStatus: status,
                    count: statusData ? statusData.count : 0
                  };
                });
                
                legalStatusNoAreaStats = {
                  areaName: "Không có khu vực",
                  totalCount: item.totalCount,
                  subjects: subjects
                };
              } else {
                // Xử lý dữ liệu có khu vực
                const subjects = Object.keys(legalStatusConfig).map(status => {
                  const statusData = item.legalStatusCounts.find(s => s.legalStatus === status);
                  return {
                    legalStatus: status,
                    count: statusData ? statusData.count : 0
                  };
                });

                legalStatusStatsMap[areaKey.toString()] = {
                  totalCount: item.totalCount,
                  subjects: subjects
                };
              }
            });

            // Xử lý dữ liệu thống kê theo dangerLevel
            const dangerLevelStatsMap = {};
            let dangerLevelNoAreaStats = null;

            dangerLevelResults.forEach(item => {
              const areaKey = item._id;
              
              if (areaKey === "no_area" || areaKey === null) {
                // Xử lý dữ liệu không có khu vực
                const subjects = Object.keys(dangerLevelConfig).map(level => {
                  const levelData = item.dangerLevelCounts.find(d => d.dangerLevel === level);
                  return {
                    dangerLevel: level,
                    count: levelData ? levelData.count : 0
                  };
                });
                
                dangerLevelNoAreaStats = {
                  areaName: "Không có khu vực",
                  totalCount: item.totalCount,
                  subjects: subjects
                };
              } else {
                // Xử lý dữ liệu có khu vực
                const subjects = Object.keys(dangerLevelConfig).map(level => {
                  const levelData = item.dangerLevelCounts.find(d => d.dangerLevel === level);
                  return {
                    dangerLevel: level,
                    count: levelData ? levelData.count : 0
                  };
                });

                dangerLevelStatsMap[areaKey.toString()] = {
                  totalCount: item.totalCount,
                  subjects: subjects
                };
              }
            });

            // Merge areas với thống kê legalStatus
            const legalStatusAreas = parentAreas.map((area) => {
              const areaId = area._id.toString();
              const stats = legalStatusStatsMap[areaId] || { 
                totalCount: 0, 
                subjects: Object.keys(legalStatusConfig).map(status => ({
                  legalStatus: status,
                  count: 0
                }))
              };
              
              return {
                areaId: area._id,
                areaName: area.name,
                totalCount: stats.totalCount,
                subjects: stats.subjects
              };
            });

            // Merge areas với thống kê dangerLevel
            const dangerLevelAreas = parentAreas.map((area) => {
              const areaId = area._id.toString();
              const stats = dangerLevelStatsMap[areaId] || { 
                totalCount: 0, 
                subjects: Object.keys(dangerLevelConfig).map(level => ({
                  dangerLevel: level,
                  count: 0
                }))
              };
              
              return {
                areaId: area._id,
                areaName: area.name,
                totalCount: stats.totalCount,
                subjects: stats.subjects
              };
            });

            // Thêm dữ liệu "Không có khu vực" cho legalStatus
            if (!legalStatusNoAreaStats) {
              legalStatusNoAreaStats = {
                areaId: null,
                areaName: "Không có khu vực",
                totalCount: 0,
                subjects: Object.keys(legalStatusConfig).map(status => ({
                  legalStatus: status,
                  count: 0
                }))
              };
            } else {
              legalStatusNoAreaStats.areaId = null;
            }
            legalStatusAreas.push(legalStatusNoAreaStats);

            // Thêm dữ liệu "Không có khu vực" cho dangerLevel
            if (!dangerLevelNoAreaStats) {
              dangerLevelNoAreaStats = {
                areaId: null,
                areaName: "Không có khu vực",
                totalCount: 0,
                subjects: Object.keys(dangerLevelConfig).map(level => ({
                  dangerLevel: level,
                  count: 0
                }))
              };
            } else {
              dangerLevelNoAreaStats.areaId = null;
            }
            dangerLevelAreas.push(dangerLevelNoAreaStats);

            // Sort theo totalCount giảm dần
            legalStatusAreas.sort((a, b) => b.totalCount - a.totalCount);
            dangerLevelAreas.sort((a, b) => b.totalCount - a.totalCount);

            const totalSubjects = legalStatusAreas.reduce((sum, area) => sum + area.totalCount, 0);

            // Tính tổng theo từng legalStatus
            const legalStatusSummary = Object.keys(legalStatusConfig).map(status => {
              const totalCount = legalStatusAreas.reduce((sum, area) => {
                const statusData = area.subjects.find(s => s.legalStatus === status);
                return sum + (statusData ? statusData.count : 0);
              }, 0);
              
              return {
                legalStatus: status,
                totalCount: totalCount
              };
            });

            // Tính tổng theo từng dangerLevel
            const dangerLevelSummary = Object.keys(dangerLevelConfig).map(level => {
              const totalCount = dangerLevelAreas.reduce((sum, area) => {
                const levelData = area.subjects.find(s => s.dangerLevel === level);
                return sum + (levelData ? levelData.count : 0);
              }, 0);
              
              return {
                dangerLevel: level,
                totalCount: totalCount
              };
            });

            result = {
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                title: `Thống kê tình trạng pháp lý`,
                summary: {
                  totalAreas: legalStatusAreas.length,
                  totalSubjects: totalSubjects,
                  legalStatusSummary: legalStatusSummary
                },
                chartConfig: {
                  colors: {
                    'pretrial': '#FF9600',
                    'trial': '#6750A4',
                    'suspended_sentence': '#33C5E8',
                    'detention': '#007CFE',
                    'arrest': '#D30500'
                  },
                  labels: {
                    'pretrial': 'Tiền án',
                    'trial': 'Tiền sự',
                    'suspended_sentence': 'Án treo',
                    'detention': 'Quản chế',
                    'arrest': 'Truy nã'
                  }
                },
                areas: legalStatusAreas,
                timeRange: {
                  type: 'all_time'
                }
              },
              dangerData: {
                title: 'Thống kê mức độ nguy hiểm',
                summary: {
                  totalAreas: dangerLevelAreas.length,
                  totalSubjects: totalSubjects,
                  dangerLevelSummary: dangerLevelSummary
                },
                chartConfig: {
                  colors: {
                    'low': '#4CAF50',
                    'medium': '#FF9800',
                    'high': '#F44336'
                  },
                  labels: {
                    'low': 'Ít nghiêm trọng',
                    'medium': 'Nghiêm trọng',
                    'high': 'Đặc biệt nghiêm trọng'
                  }
                },
                areas: dangerLevelAreas
              }
            };

            next(null, result);
          })
          .catch((err) => {
            next(err);
          });
      })
      .catch((err) => {
        next(err);
      });
  }

  // Thực thi waterfall
  async.waterfall([
    getStatisticDocument,
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
