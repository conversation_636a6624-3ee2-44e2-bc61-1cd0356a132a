const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');
const UnitModel = require('../../../models/unit');
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    unit = '',
    unitIds = [],
    data = {},
    dangerData = {}
  } = req.body;

  let statisticsData = {
  };
  let aiResponse = {};

  const validateParams = (next) => {
    if (!data || typeof data !== 'object' || Object.keys(data).length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số data là bắt buộc và phải chứa dữ liệu thống kê tình trạng pháp lý'
        }
      });
    }

    if (!dangerData || typeof dangerData !== 'object' || Object.keys(dangerData).length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số dangerData là bắt buộc và phải chứa dữ liệu thống kê mức độ nguy hiểm'
        }
      });
    }

    next();
  };
  const getStatisticsData = (next) => {
    // Chuẩn hóa dữ liệu đối tượng quản lý sang tiếng Việt
    if (data && typeof data === 'object') {
      // Tổng hợp tình trạng pháp lý
      const viLegalStatusSummary = {
        'Tổng số khu vực': data.summary?.totalAreas || 0,
        'Tổng số đối tượng': data.summary?.totalSubjects || 0,
        'Chi tiết tình trạng pháp lý': Array.isArray(data.summary?.legalStatusSummary) ? data.summary.legalStatusSummary.map(item => ({
          'Loại': data.chartConfig?.labels?.[item.legalStatus] || item.legalStatus,
          'Số lượng': item.totalCount,
          'Mã loại': item.legalStatus
        })) : []
      };

      // Thống kê theo khu vực - tình trạng pháp lý
      const viAreaLegalStatistics = Array.isArray(data.areas) ? data.areas.map(area => ({
        'Tên khu vực': area.areaName,
        'Tổng số đối tượng': area.totalCount,
        'Chi tiết theo tình trạng pháp lý': Array.isArray(area.subjects) ? area.subjects.map(subject => ({
          'Loại': data.chartConfig?.labels?.[subject.legalStatus] || subject.legalStatus,
          'Số lượng': subject.count
        })) : []
      })) : [];

      statisticsData = {
        'Tổng hợp tình trạng pháp lý': viLegalStatusSummary,
        'Thống kê khu vực theo tình trạng pháp lý': viAreaLegalStatistics
      };
    }

    // Chuẩn hóa dữ liệu mức độ nguy hiểm sang tiếng Việt
    if (dangerData && typeof dangerData === 'object') {
      // Tổng hợp mức độ nguy hiểm
      const viDangerLevelSummary = {
        'Tổng số khu vực': dangerData.summary?.totalAreas || 0,
        'Tổng số đối tượng': dangerData.summary?.totalSubjects || 0,
        'Chi tiết mức độ nguy hiểm': Array.isArray(dangerData.summary?.dangerLevelSummary) ? dangerData.summary.dangerLevelSummary.map(item => ({
          'Mức độ': dangerData.chartConfig?.labels?.[item.dangerLevel] || item.dangerLevel,
          'Số lượng': item.totalCount,
          'Mã mức độ': item.dangerLevel
        })) : []
      };

      // Thống kê theo khu vực - mức độ nguy hiểm
      const viAreaDangerStatistics = Array.isArray(dangerData.areas) ? dangerData.areas.map(area => ({
        'Tên khu vực': area.areaName,
        'Tổng số đối tượng': area.totalCount,
        'Chi tiết theo mức độ nguy hiểm': Array.isArray(area.subjects) ? area.subjects.map(subject => ({
          'Mức độ': dangerData.chartConfig?.labels?.[subject.dangerLevel] || subject.dangerLevel,
          'Số lượng': subject.count
        })) : []
      })) : [];

      statisticsData = {
        ...statisticsData,
        'Tổng hợp mức độ nguy hiểm': viDangerLevelSummary,
        'Thống kê khu vực theo mức độ nguy hiểm': viAreaDangerStatistics
      };
    }

    next();
  };


  const callAIStream = (next) => {
    const contextPrompt = `

Dữ liệu đầu vào là báo cáo thống kê đối tượng quản lý theo tình trạng pháp lý, mức độ nguy hiểm và phân bố địa bàn (toàn bộ thời gian). 
Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ của bạn: phân tích, đánh giá và đưa ra khuyến nghị theo các tiêu chí sau:
1. **Tổng quan**
   - Tổng số đối tượng, chia theo nhóm tình trạng pháp lý (tiền án, tiền sự, án treo, quản chế, truy nã).
   - Tỷ lệ % từng nhóm trong tổng số.
   - Nhận định nhóm nào nguy cơ cao, nhóm nào chiếm tỷ lệ đáng chú ý.

2. **Mức độ nguy hiểm**
   - Thống kê số lượng và tỷ lệ đối tượng theo các mức: ít nghiêm trọng, nghiêm trọng, đặc biệt nghiêm trọng.
   - Nhận xét mức nào chiếm đa số, mức nào tiềm ẩn rủi ro lớn.

3. **Phân tích theo địa bàn**
   - So sánh số lượng đối tượng giữa các địa bàn.
   - Xác định địa bàn “điểm nóng” (số lượng cao, tỷ lệ nguy hiểm cao).
   - Địa bàn nào phân bố đồng đều, địa bàn nào tập trung nhiều loại đối tượng phức tạp.

4. **Kết hợp pháp lý × mức độ nguy hiểm**
   - Phát hiện mối liên hệ: nhóm truy nã/án treo có mức độ nguy hiểm nào chiếm ưu thế.
   - Nhận định rủi ro tổng hợp cho từng địa bàn.

5. **Cảnh báo & Đề xuất**
   - Địa bàn hoặc nhóm đối tượng cần giám sát chặt.
   - Đề xuất phân bổ lực lượng, trinh sát, biện pháp quản lý.
   - Nếu có xu hướng (tăng/giảm), hãy dự báo ngắn gọn.

Trình bày ngắn gọn, rõ ràng, có số liệu minh chứng và gạch đầu dòng
Trả lời hoàn toan bằng tiếng Việt.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích kỷ luật điểm danh cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  console.log('Delta received:', delta);
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          console.log('Full AI Response:', fullContent);
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          unitIds,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};
