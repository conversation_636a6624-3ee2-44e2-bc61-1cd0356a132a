const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const mongoose = require('mongoose');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

const UserModel = require('../../../models/user');
const CriminalSubjectModel = require('../../../models/criminalSubject');
const UnitModel = require('../../../models/unit');
const AreaModel = require('../../../models/area');


const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
const UPLOAD_DIR = path.join(__dirname, '../../../../public/uploads/excel');
if (!fs.existsSync(UPLOAD_DIR)) fs.mkdirSync(UPLOAD_DIR, { recursive: true });

function validateParams({ type, endTime, options }, next) {
  if(type && !options.includes(type)) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
      }
    });
  }
  // endTime is only required if type is not 'all'
  if(type !== 'all' && !endTime) {
    return next({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: {
        head: 'Lỗi tham số',
        body: 'Tham số endTime là bắt buộc'
      }
    });
  }
  if(endTime && endTime > Date.now()) {
    endTime = Date.now();
  }
  next();
}

function calculateTimeRange(type, endTime) {
  if (type === 'all') {
    // Return null to indicate no time filtering needed
    return null;
  }
  
  const endDate = new Date(endTime);
  let startTime;
  switch (type) {
    case 'today':
      startTime = new Date(endDate);
      startTime.setHours(0, 0, 0, 0);
      break;
    case '3days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 2);
      break;
    case '7days':
      startTime = new Date(endDate);
      startTime.setDate(endDate.getDate() - 6);
      break;
    case 'week':
      startTime = new Date(endDate);
      const dayOfWeek = endDate.getDay();
      const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
      startTime.setDate(endDate.getDate() - daysToMonday);
      break;
    case 'month':
      startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      break;
    case 'year':
      startTime = new Date(endDate.getFullYear(), 0, 1);
      break;
    default:
      throw {
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: today, 3days, 7days, week, month, year, all`
        }
      };
  }
  startTime.setHours(0, 0, 0, 0);
  return startTime;
}

function getCriminalSubjects({ startTime, endTime }, next) {
  const query = {
    status: 1 // Only active records
  };
  
  // Add time filtering only if startTime is provided (not 'all' type)
  if (startTime !== null) {
    query.createdAt = { $gte: startTime.getTime(), $lte: endTime };
  }
  
  CriminalSubjectModel.find(query)
    .populate({
      path: 'managingUnit',
      select: 'name'
    })
    .populate({
      path: 'assignedOfficers',
      select: 'name idNumber'
    })
    .populate({
      path: 'areas',
      select: 'name'
    })
    .populate('createdBy', 'name')
    .lean()
    .exec((err, criminalSubjects) => {
      if (err) return next(err);
      next(null, criminalSubjects);
    });
}

function formatAndExportExcel(criminalSubjects, next) {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Criminal Subject Report');

  // Thiết lập tiêu đề
  const title = 'Báo cáo đối tượng Hình sự';
  sheet.addRow([title]);
  sheet.mergeCells(1, 1, 1, 18); // Merge across all columns
  const titleRow = sheet.getRow(1);
  titleRow.height = 35;
  titleRow.getCell(1).font = { bold: true, size: 18 };
  titleRow.getCell(1).alignment = { vertical: 'middle', horizontal: 'center' };
  titleRow.getCell(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFFFFFFF' }
  };

  // Tạo header cho bảng
  const headerRow = [
    'STT', 'Họ và tên', 'Ngày sinh', 'Giới tính', 'CMND/CCCD', 'Số điện thoại',
    'Địa chỉ thường trú', 'Địa chỉ tạm trú', 'Chỗ ở hiện tại', 'Loại vi phạm',
    'Mức độ nguy hiểm', 'Tình trạng pháp lý', 'Mô tả đối tượng', 'Tổ quản lý',
    'Cán bộ phụ trách', 'Ghi chú nghiệp vụ', 'Địa bàn', 'Thời gian tạo'
  ];
  
  sheet.addRow(headerRow);

  // Style cho header
  const headerRowObj = sheet.getRow(2);
  headerRowObj.height = 25;
  headerRowObj.eachCell((cell, colNumber) => {
    cell.font = { bold: true };
    cell.alignment = { vertical: 'middle', horizontal: 'center', wrapText: true };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFD9D9D9' }
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' }
    };
  });

  // Thiết lập độ rộng cột
  const columnWidths = [6, 20, 12, 10, 15, 15, 25, 25, 25, 15, 15, 20, 30, 20, 25, 30, 20, 18];
  columnWidths.forEach((width, index) => {
    sheet.getColumn(index + 1).width = width;
  });

  // Mapping cho các enum values
  const genderMap = {
    'male': 'Nam',
    'female': 'Nữ'
  };

  const categoryMap = {
    'security': 'An ninh',
    'criminal': 'Hình sự',
    'drug': 'Ma túy',
    'other': 'Khác'
  };

  const dangerLevelMap = {
    'low': 'Ít nghiêm trọng',
    'medium': 'Nghiêm trọng',
    'high': 'Đặc biệt nghiêm trọng'
  };

  const legalStatusMap = {
    'pretrial': 'Tiền án',
    'trial': 'Tiền sự',
    'suspended_sentence': 'Án treo',
    'detention': 'Quản chế',
    'arrest': 'Truy nã'
  };

  // Thêm dữ liệu criminal subjects
  criminalSubjects.forEach((subject, index) => {
    const row = [
      index + 1,
      subject.name || '',
      subject.dob || '',
      genderMap[subject.gender] || '',
      subject.idNumber || '',
      Array.isArray(subject.phones) ? subject.phones.join(', ') : '',
      subject.permanentAddress || '',
      subject.temporaryAddress || '',
      subject.currentResidence || '',
      categoryMap[subject.category] || subject.category || '',
      dangerLevelMap[subject.dangerLevel] || subject.dangerLevel || '',
      legalStatusMap[subject.legalStatus] || subject.legalStatus || '',
      subject.description || '',
      subject.managingUnit?.name || '',
      Array.isArray(subject.assignedOfficers) ? 
        subject.assignedOfficers.map(officer => officer.name).join(', ') : '',
      subject.businessNotes || '',
      Array.isArray(subject.areas) ? 
        subject.areas.map(area => area.name).join(', ') : '',
      subject.createdAt ? new Date(subject.createdAt).toLocaleString('vi-VN') : ''
    ];

    const addedRow = sheet.addRow(row);
    addedRow.height = 20;
    
    // Style cho các ô dữ liệu
    addedRow.eachCell((cell, colNumber) => {
      cell.alignment = { 
        vertical: 'middle', 
        horizontal: colNumber === 1 ? 'center' : 'left',
        wrapText: true
      };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });
  });

  // Tính toán thống kê
  const categoryCount = {
    'security': 0,
    'criminal': 0,
    'drug': 0,
    'other': 0
  };

  const dangerLevelCount = {
    'low': 0,
    'medium': 0,
    'high': 0
  };

  const legalStatusCount = {
    'pretrial': 0,
    'trial': 0,
    'suspended_sentence': 0,
    'detention': 0,
    'arrest': 0
  };

  criminalSubjects.forEach(subject => {
    if (categoryCount.hasOwnProperty(subject.category)) {
      categoryCount[subject.category]++;
    }
    if (dangerLevelCount.hasOwnProperty(subject.dangerLevel)) {
      dangerLevelCount[subject.dangerLevel]++;
    }
    if (legalStatusCount.hasOwnProperty(subject.legalStatus)) {
      legalStatusCount[subject.legalStatus]++;
    }
  });

  // Thêm vùng thống kê ở bên phải
  const statsStartCol = 20; // Cột T
  const statsStartRow = 2;  // Bắt đầu từ hàng 2

  // Tiêu đề thống kê
  sheet.getCell(statsStartRow, statsStartCol).value = 'THỐNG KÊ';
  sheet.mergeCells(statsStartRow, statsStartCol, statsStartRow, statsStartCol + 1);
  const statsTitle = sheet.getCell(statsStartRow, statsStartCol);
  statsTitle.font = { bold: true, size: 14 };
  statsTitle.alignment = { vertical: 'middle', horizontal: 'center' };
  statsTitle.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6E6FA' }
  };

  let currentRow = statsStartRow + 1;

  // Thống kê theo loại vi phạm
  sheet.getCell(currentRow, statsStartCol).value = 'Loại vi phạm';
  sheet.mergeCells(currentRow, statsStartCol, currentRow, statsStartCol + 1);
  currentRow++;

  Object.entries(categoryCount).forEach(([key, count]) => {
    sheet.getCell(currentRow, statsStartCol).value = categoryMap[key];
    sheet.getCell(currentRow, statsStartCol + 1).value = count;
    currentRow++;
  });

  currentRow++; // Khoảng trống

  // Thống kê theo mức độ nguy hiểm
  sheet.getCell(currentRow, statsStartCol).value = 'Mức độ nguy hiểm';
  sheet.mergeCells(currentRow, statsStartCol, currentRow, statsStartCol + 1);
  currentRow++;

  Object.entries(dangerLevelCount).forEach(([key, count]) => {
    sheet.getCell(currentRow, statsStartCol).value = dangerLevelMap[key];
    sheet.getCell(currentRow, statsStartCol + 1).value = count;
    currentRow++;
  });

  currentRow++; // Khoảng trống

  // Thống kê theo tình trạng pháp lý
  sheet.getCell(currentRow, statsStartCol).value = 'Tình trạng pháp lý';
  sheet.mergeCells(currentRow, statsStartCol, currentRow, statsStartCol + 1);
  currentRow++;

  Object.entries(legalStatusCount).forEach(([key, count]) => {
    sheet.getCell(currentRow, statsStartCol).value = legalStatusMap[key];
    sheet.getCell(currentRow, statsStartCol + 1).value = count;
    currentRow++;
  });

  currentRow++; // Khoảng trống

  // Tổng cộng
  sheet.getCell(currentRow, statsStartCol).value = 'Tổng cộng';
  sheet.getCell(currentRow, statsStartCol + 1).value = criminalSubjects.length;

  // Style cho vùng thống kê
  for (let r = statsStartRow; r <= currentRow; r++) {
    for (let c = statsStartCol; c <= statsStartCol + 1; c++) {
      const cell = sheet.getCell(r, c);
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
      if (r === statsStartRow || cell.value === 'Loại vi phạm' || cell.value === 'Mức độ nguy hiểm' || cell.value === 'Tình trạng pháp lý' || cell.value === 'Tổng cộng') {
        cell.font = { bold: true };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFD9D9D9' }
        };
      }
    }
  }

  // Thiết lập độ rộng cho cột thống kê
  sheet.getColumn(statsStartCol).width = 20;
  sheet.getColumn(statsStartCol + 1).width = 12;

  // Xuất file
  const fileName = `criminal_subject_report_${Date.now()}.xlsx`;
  const filePath = path.join(UPLOAD_DIR, fileName);
  
  workbook.xlsx.writeFile(filePath)
    .then(() => {
      const downloadLink = `/uploads/excel/${fileName}`;
      next(null, { code: CONSTANTS.CODE.SUCCESS, link: downloadLink });
    })
    .catch(next);
}

module.exports = (req, res) => {
  const userId = req.user.id;
  const options = [
    'today', '3days','7days','week','month','year','all',
  ];
  let { type = 'week', endTime, startTime } = req.body;

  async.waterfall([
    function stepValidateParams(next) {
      validateParams({ type, endTime, options }, next);
    },
    function stepCalculateTimeRange(next) {
      if (startTime) {
        // Nếu đã có startTime từ body thì dùng luôn
        next(null, new Date(startTime));
      } else {
        try {
          const calculatedStartTime = calculateTimeRange(type, endTime);
          next(null, calculatedStartTime); // calculatedStartTime có thể là null nếu type = 'all'
        } catch (err) {
          next(err);
        }
      }
    },
    function stepGetCriminalSubjects(startTime, next) {
      getCriminalSubjects({ startTime, endTime }, next);
    },
    formatAndExportExcel
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });
    res.json(data || err);
  });
}
