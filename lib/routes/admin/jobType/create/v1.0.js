const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const { change_alias } = require('../../../../util/tool');

const JobTypeModel = require('../../../../models/jobType');
const UnitModel = require('../../../../models/unit');

module.exports = (req, res) => {
  const userId = req.user._id;
  const { name, description = '', unitId, quickReport = true, detailReport = true, status } = req.body;

  let jobTypeData;
  let unitInfo;

  const checkParams = (next) => {

    // Kiểm tra tên công việc
    if (!name || !_.isString(name) || name.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên công việc không được để trống'
        }
      });
    }

    const trimmedName = name.trim();
    if (trimmedName.length < 3 || trimmedName.length > 100) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Tên công việc phải từ 3-100 ký tự'
        }
      });
    }

    // Kiểm tra tổ
    if (!unitId || !_.isString(unitId) || unitId.trim() === '') {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng chọn tổ thực hiện công việc'
        }
      });
    }

    // Kiểm tra trạng thái
    if (status !== undefined && ![0, 1].includes(status)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Trạng thái không hợp lệ'
        }
      });
    }

    jobTypeData = {
      name: trimmedName,
      description: description.trim(),
      unitId: unitId.trim(),
      quickReport: quickReport,
      detailReport: detailReport
    };

    if (status !== undefined) {
      jobTypeData.status = status;
    }

    next();
  };

  const checkUnit = (next) => {
    UnitModel.findById(jobTypeData.unitId, (err, unit) => {
      if (err) {
        logger.logError('Lỗi khi tìm tổ:', err);
        return next(err);
      }

      if (!unit || unit.status !== 1) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Tổ không tồn tại. Vui lòng kiểm tra lại.'
          }
        });
      }

      unitInfo = unit;

      next();
    });
  };

  const checkDuplicateName = (next) => {
    JobTypeModel.findOne({
      name: jobTypeData.name,
      status: 1 // Chỉ kiểm tra công việc đang hoạt động
    }, (err, existingJobType) => {
      if (err) {
        logger.logError('Lỗi khi kiểm tra tên trùng lặp:', err);
        return next(err);
      }

      if (existingJobType) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Công việc "${jobTypeData.name}" đã tồn tại. Vui lòng kiểm tra lại.`
          }
        });
      }

      next();
    });
  };

  const createJobType = (next) => {
    // Tạo nameAlias tự động từ name
    const nameAlias = change_alias(jobTypeData.name);
    const jobType = {
      name: jobTypeData.name,
      nameAlias: nameAlias,
      description: jobTypeData.description,
      unit: jobTypeData.unitId,
      quickReport: jobTypeData.quickReport,
      detailReport: jobTypeData.detailReport
    };

    if (jobTypeData.status !== undefined) {
      jobType.status = jobTypeData.status;
    }

    const newJobType = new JobTypeModel(jobType);

    newJobType.save((err, savedJobType) => {
      if (err) {
        logger.logError('Lỗi khi tạo công việc:', err);
        return next(err);
      }

      // Populate thông tin unit để trả về
      JobTypeModel.findById(savedJobType._id)
        .populate('unit', 'name')
        .exec((err, populatedJobType) => {
          if (err) {
            logger.logError('Lỗi khi populate JobType:', err);
            return next(err);
          }

          logger.logInfo(`Tạo công việc thành công - Name: ${jobTypeData.name}, Tổ: ${unitInfo.name}, User: ${userId}`);

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Tạo công việc thành công'
            },
            data: populatedJobType
          });
        });
    });
  };

  // Thực thi các bước
  async.waterfall([
    checkParams,
    checkUnit,
    checkDuplicateName,
    createJobType
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
    });

    res.json(data || err);
  });
};
