const _ = require("lodash");
const async = require("async");
const ms = require("ms");
const config = require("config");
const util = require("util");
const rp = require("request-promise");
const Joi = require("joi");
Joi.objectId = require("joi-objectid")(Joi);
const User = require("../../../../models/user");
const PositionModel = require("../../../../models/position");
const RankModel = require("../../../../models/rank");
const GroupPermissionModel = require("../../../../models/groupPermission");
const StatisticsTrigger = require("../../../../utils/statisticsTrigger");
const CONSTANTS = require("../../../../const");
const MESSAGES = require("../../../../message");
const tool = require("../../../../util/tool");
const MailUtil = require("../../../../util/mail");
const validator = require("validator");
const redisConnection = require("../../../../connections/redis");
const UnitModel = require('../../../../models/unit');


module.exports = (req, res) => {
  const {username, name, phones, email, gender, avatar, dob, apps, address, rank, educationLevel, politicalTheoryLevel, idNumber, managedUnits} = req.body || ''
  let { units, areas, jobTypes, permissions, groupPermissions, positions } = req.body || [];
  const _id = req.body._id || "";
  let permissionInGroup = [];
  const userId = _.get(req,'user.id', '')
  let objUpdate = {};
  let updatedData = {};
  let rankImage;
  let role = 'officer';
  if(units && units.length && units.length === 1) {
    role = 'leader'
  } 
  const checkParams = (next) => {
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
      });
    }
    if (!username || (username && !username.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_USERNAME,
      });
    }
    if (!email || (email && !email.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_EMAIL,
      });
    }
    if (!validator.isEmail(email)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.EMAIL_NOT_VALID,
      });
    }
    if(!idNumber || (idNumber && !idNumber.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_IDNUMBER
      })
    }
    if(!/^\d{6}$/.test(idNumber.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: 'Số hiệu phải là 6 chữ số'
      })
    }
    if (!name || (name && !name.trim())) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_NAME,
      });
    }
    if(!phones.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_PHONE
      })
    }
    if(!phones.every(phone => validator.isMobilePhone(phone, ['vi-VN']))) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.PHONE_NOT_VALID
      })
    }
    if (!gender) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER,
      });
    }
    if (!["male", "female"].includes(gender)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_GENDER,
      });
    }

    if (!apps || apps && !apps.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_APPS,
      });
    }
    if(!['cms','ioc'].includes(...apps)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.APPS_NOT_VALID
      })
    }
    if (!units || !units.length) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.USER.NOT_FOUND_UNIT,
      });
    }
    if (managedUnits && managedUnits.length) {
      if (!positions || !positions.length) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: 'Nếu có tổ phụ trách thì phải chức vụ phải là chức vụ của lãnh đạo'
        });
      }
      PositionModel.find({
        _id: { $in: positions },
        special: true
      }).lean().exec((err, results) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        if (!results || !results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Nếu có tổ phụ trách thì phải chức vụ phải là chức vụ của lãnh đạo'
            }
          });
        }
        UnitModel.find({ _id: { $in: managedUnits } }).lean().exec((err2, unitsResult) => {
          if (err2) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          if (!unitsResult || unitsResult.length !== managedUnits.length) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Tổ phụ trách không tồn tại hoặc có tổ không hợp lệ'
              }
            });
          }
          const invalidUnits = unitsResult.filter(u => !u.parent);
          if (invalidUnits.length) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Tổ phụ trách chỉ được chọn các Tổ công tác, không được chọn các tổ cấp trên'
              }
            });
          }
          next(null);
        });
      });
      return;
    }
    next(null);
  };

  const checkUserExists = (next) => {
    User.find({
      _id: {
        $ne: _id,
      },
      $or:[
        {
          username
        },
        {
          email
        },
        {
          phones: {
            $in: phones
          }
        },
        {
          idNumber
        }
      ],
      status: 1,
    })
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        if (results.length) {
          // Kiểm tra cụ thể trường nào bị trùng để trả về message phù hợp
          const existingUser = results[0];
          let errorMessage = MESSAGES.USER.EXISTS;

          if(existingUser.username === username) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Tên đăng nhập đã tồn tại'
            };
          } else if(existingUser.email === email) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Email cán bộ đã tồn tại'
            };
          } else if(existingUser.phones.some(phone => phones.includes(phone))) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Số điện thoại cán bộ đã tồn tại'
            };
          } else if(existingUser.idNumber === idNumber) {
            errorMessage = {
              head: 'Thông báo',
              body: 'Số hiệu cán bộ đã tồn tại'
            };
          }

          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: errorMessage,
          });
        }
        next();
      });
  };

  const findOldUser = (next) => {
    User.findOne({
      _id,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        oldUser = result;
        // if(oldUser.positions && !_.isEqual(oldUser.positions, positions) && userId === oldUser._id.toString()) {
        //   return next({
        //     code: CONSTANTS.CODE.WRONG_PARAMS,
        //     message: MESSAGES.USER.NOT_UPDATE_UNIT_YOURSELF,
        //   });
        // }
        next();
      });
  };

  const findRankImage = (next) => {
    if (!rank) {
      return next();
    }

    RankModel
      .findOne({ name: rank })
      .select('image')
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err)
        }
        if(result && result.image) {
          rankImage = result.image;
        }
        next();
      })
  }

  const findPermissions = (next) => {
     if (!positions.length) {
      return next();
    }
    if (oldUser.positions && _.isEqual(oldUser.positions, positions)) {
      return next();
    }
    PositionModel.find({
      _id: {
        $in: positions
      },
    })
      .select("permissions groupPermissions isTeamLeader")
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.forEach((pos) => {
          permissions = _.union(permissions, pos.permissions.map(String));
          groupPermissions = _.union(groupPermissions, pos.groupPermissions.map(String));
        });
        if(results && results.some(pos => pos.isTeamLeader === true)) {
          role = 'team_leader'
        }
        next();
      });
  };

  const findPermissionsInGroup = (next) => {
    GroupPermissionModel.find({
      _id: {
        $in: groupPermissions,
      },
    })
      .populate("permissions", "code -_id")
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }
        results.map((groupPermission) => {
          permissionInGroup = permissionInGroup.concat(groupPermission.permissions);
        });
        next();
      });
  }

  const setDefaultAvatar = (next) => {
    // Nếu không có avatar, thiết lập avatar mặc định dựa trên giới tính
    if (!avatar || (avatar && !avatar.trim())) {
      if (gender === 'female') {
        req.body.avatar = 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-22-image2.png';
      } else if (gender === 'male') {
        req.body.avatar = 'https://media-hb-dev.canbo.ai/uploads/avatar/2025-08-22-image12.png';
      }
    }
    next();
  }

  const updateUser = (next) => {
    objUpdate = {
      username,
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()), // Tự động cập nhật nameAlias từ name
      idNumber,
      dob,
      gender,
      address,
      email,
      phones,
      avatar: req.body.avatar || avatar,
      rank,
      rankImage,
      units,
      areas, // thêm trường areas để cập nhật danh sách khu vực địa bàn mà cán bộ quản lý
      jobTypes,
      positions,
      educationLevel,
      politicalTheoryLevel,
      apps,
      updatedAt: Date.now(),
      managedUnits
    };
    if (permissions.length) {
      objUpdate.permissions = permissions;
    }
    if (groupPermissions.length) {
      objUpdate.groupPermissions = groupPermissions;
    }
    User.findOneAndUpdate(
      {
        _id,
      },
      objUpdate,
      {new: true}
    )
    .populate("permissions", "code _id")
    .lean()
    .exec((err, result) => {
      if (err) {
        return next(err);
      }
      updatedData = {
        ...result,
        permissions: result.permissions.map((permission) => permission._id),
      }
      result.permissions.forEach((permission) => {
        delete permission._id
      })
      if (permissions.length || groupPermissions.length) {
        redisConnection("master")
          .getConnection()
          .get(`user:${_id}`, (err, token) => {
            if (token) {
              const obj = {
                id: _id,
                permissions: [...result.permissions, ...permissionInGroup],
                groupPermissions,
                role
              };
              redisConnection("master")
                .getConnection()
                .multi()
                .set(`user:${token}`, JSON.stringify(obj))
                .exec((err, result) => {});
            }
          });
        redisConnection("master")
          .getConnection()
          .get(`ioc:${_id}`, (err, token) => {
            if (token) {
              const obj = {
                id: _id,
                permissions: [...result.permissions, ...permissionInGroup],
                groupPermissions,
                role
              };
              redisConnection("master")
                .getConnection()
                .multi()
                .set(`ioc:${token}`, JSON.stringify(obj))
                .exec((err, result) => {});
            }
          });
      } else {
        redisConnection("master")
          .getConnection()
          .get(`user:${_id}`, (err, token) => {
            if (token) {
              redisConnection("master")
                .getConnection()
                .get(`user:${token}`, (err, data) => {
                  const dataObj = JSON.parse(data);
                  if(!dataObj.role || dataObj.role !== role) {
                    dataObj.role = role
                    redisConnection("master")
                    .getConnection()
                    .multi()
                    .set(`user:${token}`, JSON.stringify(dataObj))
                    .exec((err, result) => {
                    });
                  }
                })
            }
          });
        redisConnection("master")
          .getConnection()
          .get(`ioc:${_id}`, (err, token) => {
            if (token) {
              redisConnection("master")
                .getConnection()
                .get(`ioc:${token}`, (err, data) => {
                  const dataObj = JSON.parse(data);
                  if(!dataObj.role || dataObj.role !== role) {
                    dataObj.role = role
                    redisConnection("master")
                    .getConnection()
                    .multi()
                    .set(`ioc:${token}`, JSON.stringify(dataObj))
                    .exec((err, result) => {
                    });
                  }
                })
            }
          });
      }
      next();
    });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.USER.UPDATE_SUCCESS,
    });

    // Trigger statistics update khi cập nhật user
    try {
      StatisticsTrigger.triggerUserUpdate('update', {
        _id: _id,
        status: updatedData.status,
        units: updatedData.units,
        areas: updatedData.areas,
        positions: updatedData.positions
      });
    } catch (error) {
      console.error('Error triggering user update:', error);
    }

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_user',
        description: 'Cập nhật người dùng',
        data: objUpdate,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall(
    [checkParams, checkUserExists, findOldUser, findRankImage, findPermissions, findPermissionsInGroup, setDefaultAvatar, updateUser, writeLog],
    (err, data) => {
      err &&
        _.isError(err) &&
        (data = {
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
        });

      res.json(data || err);
    }
  );
};
