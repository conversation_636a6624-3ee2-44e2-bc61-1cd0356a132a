const _ = require("lodash");
const async = require("async");
const Joi = require("joi");
Joi.objectId = require("joi-objectid")(Joi);

const User = require("../../../../models/user");
const DutyShiftModel = require("../../../../models/dutyShift");
const LeaveRequestModel = require("../../../../models/leaveRequest");
const CONSTANTS = require("../../../../const");
const MESSAGES = require("../../../../message");

// Import utility function để xử lý cấu trúc phân cấp areas
const { processAreasHierarchy } = require('../../../../util/areasHierarchy');
const { change_alias } = require('../../../../util/tool');

module.exports = (req, res) => {
  const limit = _.get(req, "body.limit", 10);
  const page = _.get(req, "body.page", 0);
  const sort = _.get(req, "body.sort", 1);
  const isFilter = _.get(req, "body.isFilter");
  const textSearch = _.get(req, "body.textSearch", "");
  const active = _.get(req, "body.active", "");
  const unit = _.get(req, "body.unit", "");
  const positions = _.get(req, "body.positions", []);
  const gender = _.get(req, "body.gender", "");
  const excludes = _.get(req, "body.excludes", []);
  const isLeader = _.get(req, "body.isLeader", false);
  const includeJobStatus = _.get(req, 'body.includeJobStatus', false);
  const onlySelectCombatDutyShift = _.get(req, 'body.onlySelectCombatDutyShift', false);
  const onlySelectLocationDutyShift = _.get(req, 'body.onlySelectLocationDutyShift', false);
  const workStatusFilter = _.get(req, 'body.workStatusFilter', ''); // 'busy', 'free', hoặc '' (tất cả)
  const startTime = _.get(req, 'body.startTime', null);
  const endTime = _.get(req, 'body.endTime', null);

  let obj = { status: 1 };
  let count = 0;
  let data = [];
  const checkParams = (next) => {
    if (textSearch && textSearch.trim()) {
      const $regex = change_alias(textSearch.trim());
      obj["$or"] = [
        {
          nameAlias: {
            $regex,
            $options: "i",
          },
        },
        {
          idNumber: {
            $regex,
            $options: "i",
          },
        },
        {
          phones: {
            $regex,
            $options: "i",
          },
        },
        {
          code: {
            $regex,
            $options: "i",
          },
        },
      ];
    }

    if (isFilter) {
      if (_.isNumber(active)) {
        obj.active = active;
      }
      if (unit) {
        obj.units = unit;
      }
      if (positions.length) {
        obj.positions = {
          $in: positions
        }
      }
      if (gender) {
        obj.gender = gender;
      }
    }

    if (excludes.length) {
      obj._id = { $nin: excludes };
    }
    if (isLeader) {
      obj.units = {
        $size: 1, // Chỉ lấy những user có đúng 1 tổ
      };
    }

    if (includeJobStatus) {
      if (!startTime) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: "Thiếu tham số Thời gian bắt đầu"
          }
        });
      }

      // Validate endTime nếu được truyền
      if (endTime) {
        // Validate endTime is after startTime
        if (endTime <= startTime ) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: "Thời gian kết thúc phải sau Thời gian bắt đầu"
            }
          });
        }
        if(endTime - startTime > 2*24*60*60*1000) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: "Khoảng thời gian không được vượt quá 2 ngày"
            }
          });
        }
      }
    }

    next();
  };

   const findUserLocationDutyShift = (next) => {
    if (!onlySelectLocationDutyShift) {
      return next();
    }

    // Tạo query dựa trên startTime và endTime (nếu có)
    const shiftQuery = {
      status: 1,
      source: {
        $in: ['location','patrol']
      }
    };

    if (endTime) {
      // Nếu có cả startTime và endTime, tìm ca trực overlap với khoảng thời gian
      shiftQuery.startTime = { $lt: endTime };
      shiftQuery.endTime = { $gt: startTime };
    } else {
      // Nếu chỉ có startTime, tìm ca trực đang diễn ra tại thời điểm startTime
      shiftQuery.startTime = { $lt: startTime };
      shiftQuery.endTime = { $gt: startTime };
    }

    // Tìm tất cả các user có ca trực theo điều kiện
    DutyShiftModel.distinct('officer', shiftQuery)
    .lean()
    .exec((err, userIds) => {
      if (err) {
        return next(err);
      }

      obj._id = { $in: userIds };
      next();
    });
  }

  const findUserInCombatDutyShift = (next) => {
    if (!onlySelectCombatDutyShift) {
      return next();
    }

    // Tạo query dựa trên startTime và endTime (nếu có)
    const shiftQuery = {
      status: 1,
      source: {
        $in: ['main','sub']
      },
      isCriminal: { $ne: true } 
    };

    if (endTime) {
      // Nếu có cả startTime và endTime, tìm ca trực overlap với khoảng thời gian
      shiftQuery.startTime = { $lt: endTime };
      shiftQuery.endTime = { $gt: startTime };
    } else {
      // Nếu chỉ có startTime, tìm ca trực đang diễn ra tại thời điểm startTime
      shiftQuery.startTime = { $lt: startTime };
      shiftQuery.endTime = { $gt: startTime };
    }

    // Tìm tất cả các user có ca trực theo điều kiện
    DutyShiftModel.distinct('officer', shiftQuery)
    .lean()
    .exec((err, userIds) => {
      if (err) {
        return next(err);
      }

      obj._id = { $in: userIds };
      next();
    });
  }


  const countUser = (next) => {
    // Khi cần lọc theo workStatus hoặc includeJobStatus, không cần đếm ở đây
    if (workStatusFilter || includeJobStatus) {
      count = 0; 
      return next();
    }
    
    User.countDocuments(obj)
      .lean()
      .exec((err, total) => {
        count = Math.ceil(total / limit);
        next();
      });
  };

  const listUser = (next) => {
    const options = {
      sort: sort == 1 ? "createdAt" : "-createdAt",
    };
    
    // Nếu cần lọc theo workStatus hoặc includeJobStatus, không phân trang ở đây
    if (!workStatusFilter && !includeJobStatus) {
      options.limit = limit;
      options.skip = page * limit;
    }
    
    User.find(obj, "-password", options)
      .populate("permissions", "name code")
      .populate({
        path: "units",
        select: "name parentPath",
        populate: {
          path: "parentPath",
          select: "name icon",
        },
      })
      .populate("positions", "name unit special")
      .populate("categories", "name icon")
      .populate("jobTypes", "name")
      .populate({
        path: "areas",
        select: "name level parent parentPath",
        populate: {
          path: "parent",
          select: "name",
        },
      })
      .populate({
        path: "groupPermissions",
        select: "name permissions",
        populate: {
          path: "permissions",
          select: "name",
        },
      })
      .populate('managedUnits', 'name icon')
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        // Xử lý cấu trúc phân cấp areas cho từng user
        if (results && Array.isArray(results)) {
          results.forEach(user => {
            if (user.areas && user.areas.length > 0) {
              user.areas = processAreasHierarchy(user.areas);
            }
          });
        }
        data = results;
        next(null);
      });
  };

  const listShifts = (next) => {
    if (!includeJobStatus) {
      return next();
    }

    // Query duty shifts cho từng user
    async.eachSeries(data, (user, callback) => {
      // Tạo query dựa trên startTime và endTime (nếu có)
      const shiftQuery = {
        status: 1,
        officer: user._id
      };

      if (endTime) {
        // Nếu có cả startTime và endTime, tìm ca trực overlap với khoảng thời gian
        shiftQuery.startTime = { $lt: endTime };
        shiftQuery.endTime = { $gt: startTime };
      } else {
        // Nếu chỉ có startTime, tìm ca trực đang diễn ra tại thời điểm startTime
        shiftQuery.startTime = { $lt: startTime };
        shiftQuery.endTime = { $gt: startTime };
      }

      DutyShiftModel.find(shiftQuery)
        .select('name startTime endTime')
        .lean()
        .exec((err, shifts) => {
          if (err) {
            return callback(err);
          }

          // Thêm dutyShifts vào user object
          user.dutyShifts = shifts || [];
          user.workStatus = user.dutyShifts.length > 0 ? 'busy' : 'free';
          callback();
        });
    }, (err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  }

  const listsLeaveRequest = (next) => {
    if (!includeJobStatus) {
      return next();
    }

    // Query leave requests cho từng user
    async.eachSeries(data, (user, callback) => {
      // Tạo query dựa trên startTime và endTime (nếu có)
      const leaveQuery = {
        status: 'approved',
        user: user._id
      };

      if (endTime) {
        // Nếu có cả startTime và endTime, tìm đơn nghỉ phép overlap với khoảng thời gian
        leaveQuery.startTime = { $lt: endTime };
        leaveQuery.endTime = { $gt: startTime };
      } else {
        // Nếu chỉ có startTime, tìm đơn nghỉ phép đang có hiệu lực tại thời điểm startTime
        leaveQuery.startTime = { $lt: startTime };
        leaveQuery.endTime = { $gt: startTime };
      }

      LeaveRequestModel.find(leaveQuery)
        .select('type startDate endDate startTime endTime reason')
        .lean()
        .exec((err, leaveRequests) => {
          if (err) {
            return callback(err);
          }

          // Thêm leaveRequests vào user object
          user.leaveRequests = leaveRequests || [];
          if(user.leaveRequests.length  > 0) {
            user.workStatus = leaveRequests[0].type;
          }

          callback();
        });
    }, (err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  }

  const filterAndPaginateByWorkStatus = (next) => {
    // Sắp xếp theo workStatus nếu includeJobStatus = true (free lên trước, sau đó business_trip, busy, leave)
    if (includeJobStatus) {
      data.sort((a, b) => {
        // Thứ tự ưu tiên: free -> business_trip -> busy -> leave
        const statusOrder = {
          'free': 1,
          'busy': 2,
          'business_trip': 3,
          'leave': 4
        };
        
        const statusA = statusOrder[a.workStatus] || 999;
        const statusB = statusOrder[b.workStatus] || 999;
        
        if (statusA !== statusB) {
          return statusA - statusB;
        }
        
        // Nếu cùng workStatus, sắp xếp theo thời gian tạo (giữ nguyên sort gốc)
        if (sort == 1) {
          return new Date(a.createdAt) - new Date(b.createdAt);
        } else {
          return new Date(b.createdAt) - new Date(a.createdAt);
        }
      });
    }

    // Lọc theo workStatus nếu có
    if (workStatusFilter && includeJobStatus) {
      data = data.filter(user => user.workStatus === workStatusFilter);
    }

    // Áp dụng phân trang sau khi đã lọc (nếu cần)
    if (workStatusFilter || includeJobStatus) {
      const totalAfterFilter = data.length;
      count = Math.ceil(totalAfterFilter / limit);
      
      const skip = page * limit;
      data = data.slice(skip, skip + limit);
    }

    next();
  }

  const handleDataReturn = (next) => {
    return next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data,
      count
    });
  }

  async.waterfall([checkParams, findUserLocationDutyShift, findUserInCombatDutyShift, countUser, listUser, listShifts, listsLeaveRequest, filterAndPaginateByWorkStatus, handleDataReturn], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
