const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const MissionModel = require('../../../../models/mission');
const MissionLogModel = require('../../../../models/missionLog');
const UserModel = require('../../../../models/user');
const PushNotifyManager = require('../../../../jobs/pushNotify');
const PositionModel = require('../../../../models/position');

// Function to send completion notification to related users
const sendCompleteNotification = (mission, callback) => {
  const notificationTitle = 'Thông báo hoàn thành nhiệm vụ';
  const notificationMessage = `Nhiệm vụ "${mission.name}" đã đượ<PERSON> hoàn tất`;
  const notificationData = {
    link: 'MissionDetailScreen',
    extras: {
      _id: mission._id.toString(),
    },
    linkWeb: `/manage-mission/${mission._id}`
  };

  async.waterfall([
    // Get all users to notify
    (cb) => {
      let userIdsToNotify = [...mission.users]; // Start with mission.users

      // If mission type is unit_assign, also get team leaders from assignInfo
      if (mission.type === 'unit_assign' && mission.assignInfo && mission.assignInfo.length > 0) {
        // Get all unit IDs from assignInfo
        const unitIds = mission.assignInfo.map(info => info.unit);
        
        // Find all team leaders in these units
        UserModel.find({
          units: { $in: unitIds },
          status: 1 // Only active users
        })
        .populate({
          path: 'positions',
          model: 'Position',
          match: { isTeamLeader: true, status: 1 }
        })
        .exec((err, users) => {
          if (err) return cb(err);
          
          // Filter users who have team leader positions
          const teamLeaders = users.filter(user => 
            user.positions && user.positions.length > 0
          );
          
          // Add team leader IDs to notification list
          const teamLeaderIds = teamLeaders.map(leader => leader._id);
          userIdsToNotify = [...userIdsToNotify, ...teamLeaderIds];
          
          // Remove duplicates
          userIdsToNotify = [...new Set(userIdsToNotify.map(id => id.toString()))];
          
          cb(null, userIdsToNotify);
        });
      } else {
        cb(null, userIdsToNotify);
      }
    },
    // Send notifications to all users
    (userIds, cb) => {
      if (!userIds || userIds.length === 0) {
        return cb(null);
      }

      // Send notifications in parallel
      async.each(userIds, (userId, notifyCallback) => {
        PushNotifyManager.sendToMember(
          userId,
          notificationTitle,
          notificationMessage,
          notificationData,
          'mission_update',
          'ioc'
        )
        .then(() => {
          notifyCallback();
        })
        .catch((err) => {
          console.error(`Error sending notification to user ${userId}:`, err);
          notifyCallback(); // Continue even if one notification fails
        });
      }, (err) => {
        cb(err);
      });
    }
  ], callback);
};

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id } = req.body;

  // Validate required parameters
  if (!_id) {
    return res.json({
      code: CONSTANTS.CODE.WRONG_PARAMS,
      message: MESSAGES.SYSTEM.WRONG_PARAMS,
    });
  }

  async.waterfall([
    // Check if mission exists
    (callback) => {
      MissionModel.findById(_id, (err, mission) => {
        if (err) return callback(err);
        if (!mission) {
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: MESSAGES.SYSTEM.WRONG_PARAMS,
          });
        }
        // Check if mission can be completed (must be in progress)
        if (mission.status === 3) { // COMPLETED
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head:'Thông báo',
              body:'Nhiệm vụ đã hoàn thành'
            }
          });
        }
        if (mission.status === 4) { // CANCELLED
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head:'Thông báo',
              body:'Nhiệm vụ đã bị hủy bỏ, không thể hoàn thành'
            }
          });
        }
        if (mission.status !== 2) { // Must be IN_PROGRESS
          return callback({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head:'Thông báo',
              body:'Nhiệm vụ phải ở trạng thái đang thực hiện mới có thể hoàn thành'
            }
          });
        }
        callback(null, mission);
      });
    },
    // Update mission status to completed
    (mission, callback) => {
      MissionModel.findByIdAndUpdate(
        _id,
        {
          status: 3, // COMPLETED
          updatedAt: Date.now(),
        },
        { new: true },
        (err, updatedMission) => {
          if (err) return callback(err);
          callback(null, updatedMission);
        }
      );
    },
    // Create mission log entry
    (updatedMission, callback) => {
      const missionLog = new MissionLogModel({
        user: userId,
        mission: _id,
        message: `Hoàn thành nhiệm vụ`,
        action: 3
      });

      missionLog.save((err) => {
        if (err) return callback(err);
        callback(null, updatedMission);
      });
    },
    // Send completion notification to related users
    (updatedMission, callback) => {
      sendCompleteNotification(updatedMission, (err) => {
        if (err) {
          console.error('Error sending complete notification:', err);
          // Don't fail the entire operation if notification fails
        }
        callback(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head:'Thông báo',
            body:'Hoàn thành nhiệm vụ thành công'
          },
          data: updatedMission,
        });
      });
    },
  ], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
