const _ = require('lodash')
const async = require('async')
const MissionModel = require('../../../../models/mission')
const MissionLogModel = require('../../../../models/missionLog')
const SavedNotificationModel = require('../../../../models/savedNotification')
const UserModel = require('../../../../models/user')
const UnitModel = require('../../../../models/unit')
const PushNotifyManager = require('../../../../jobs/pushNotify')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const MailUtil = require('../../../../util/mail')
const logger = require('../../../../logger')

module.exports = (req, res) => {
  const {
   _id,
   users,
   unit
  } = req.body

  const validateParams = (next) => {
    if (!_id || !_id.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID nhiệm vụ là bắt buộc'
        }
      });
    }

    if (!users || !Array.isArray(users)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Danh sách ID người dùng là bắt buộc và phải là mảng'
        }
      });
    }

    // Validate từng user ID
    for (let i = 0; i < users.length; i++) {
      if (!users[i] || !users[i].trim()) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `ID người dùng thứ ${i + 1} không hợp lệ`
          }
        });
      }
    }

    if (!unit || !unit.trim()) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID tổ là bắt buộc'
        }
      });
    }

    next();
  }

  const checkMissionStatus = (next) => {
    MissionModel.findById(_id.trim())
      .then(mission => {
        if (!mission) {
          return next({
            code: CONSTANTS.CODE.FAIL,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy nhiệm vụ'
            }
          });
        }

        if (mission.status !== 2) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Chỉ có thể thêm user khi nhiệm vụ ở trạng thái Đang Huy động lực lượng'
            }
          });
        }

        next(null, mission);
      })
      .catch(err => next(err));
  }

  const updateMission = (mission, next) => {
    // Lưu danh sách users hiện tại của unit để so sánh
    let currentUsersInUnit = [];
    const unitAssignInfo = mission.assignInfo.find(info => info.unit.toString() === unit.trim());
    if (unitAssignInfo) {
      currentUsersInUnit = unitAssignInfo.users.map(u => u.toString());
    }

    // Nếu users rỗng, chỉ cần validate unit và cập nhật
    if (users.length === 0) {
      // Tìm assignInfo cho unit tương ứng
      let unitAssignInfo = mission.assignInfo.find(info => info.unit.toString() === unit.trim());
      
      if (!unitAssignInfo) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Không tìm thấy thông tin phân công cho tổ này'
          }
        });
      }

      // Lấy danh sách users hiện tại của unit để xóa
      const currentUsersInUnit = unitAssignInfo.users.map(u => u.toString());
      
      // Xóa hết users của unit này
      unitAssignInfo.users = [];

      // Xóa users khỏi mission.users nếu không còn trong bất kỳ unit nào
      const missionUsersSet = new Set(mission.users.map(u => u.toString()));
      currentUsersInUnit.forEach(userId => {
        const stillInOtherUnits = mission.assignInfo.some(info => 
          info.unit.toString() !== unit.trim() && 
          info.users.some(u => u.toString() === userId)
        );
        if (!stillInOtherUnits) {
          missionUsersSet.delete(userId);
        }
      });

      mission.users = Array.from(missionUsersSet);
      mission.updatedAt = Date.now();

      mission.save()
        .then(() => {
          return MissionModel.findById(mission._id)
            .populate('createdBy', 'name')
            .populate('assignInfo.unit', 'name')
            .populate('assignInfo.users', 'name phones idNumber avatar')
            .populate('users', 'name phones idNumber avatar');
        })
        .then(updatedMission => {
          // Không có users mới được thêm khi users = [], chỉ có users bị xóa
          next(null, updatedMission, [], currentUsersInUnit);
        })
        .catch(err => next(err));
      
      return;
    }

    // Validate tất cả users trước khi thực hiện update
    const userPromises = users.map(userId => UserModel.findById(userId.trim()));
    
    Promise.all(userPromises)
      .then(userDocs => {
        // Kiểm tra tất cả users có tồn tại không
        for (let i = 0; i < userDocs.length; i++) {
          if (!userDocs[i]) {
            return next({
              code: CONSTANTS.CODE.FAIL,
              message: {
                head: 'Thông báo',
                body: `Không tìm thấy người dùng thứ ${i + 1}`
              }
            });
          }
        }

        // Kiểm tra tất cả users có thuộc unit không
        // for (let i = 0; i < userDocs.length; i++) {
        //   const userUnits = userDocs[i].units.map(u => u.toString());
        //   if (!userUnits.includes(unit.trim())) {
        //     return next({
        //       code: CONSTANTS.CODE.WRONG_PARAMS,
        //       message: {
        //         head: 'Thông báo',
        //         body: `Người dùng thứ ${i + 1} không thuộc tổ này`
        //       }
        //     });
        //   }
        // }

        // Tìm assignInfo cho unit tương ứng
        let unitAssignInfo = mission.assignInfo.find(info => info.unit.toString() === unit.trim());
        
        if (!unitAssignInfo) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy thông tin phân công cho tổ này'
            }
          });
        }

        // Kiểm tra numberOfUsers phải > 0
        if (!unitAssignInfo.numberOfUsers || unitAssignInfo.numberOfUsers <= 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Tổ này không được phép có user (numberOfUsers = 0)'
            }
          });
        }

        // Kiểm tra số lượng users mới có vượt quá numberOfUsers không
        if (users.length > unitAssignInfo.numberOfUsers) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Vượt quá số lượng cán bộ cho phép',
              body: `Số cán bộ tối đa cho phép điều động là: ${unitAssignInfo.numberOfUsers}`
            }
          });
        }

        // Lấy danh sách users hiện tại của unit
        const currentUsersInUnit = unitAssignInfo.users.map(u => u.toString());
        const newUsers = users.map(u => u.trim());

        // Tìm users cần thêm và users cần xóa
        const usersToAdd = newUsers.filter(userId => !currentUsersInUnit.includes(userId));
        const usersToRemove = currentUsersInUnit.filter(userId => !newUsers.includes(userId));

        // Cập nhật assignInfo của unit - thay thế hoàn toàn
        unitAssignInfo.users = newUsers;

        // Cập nhật mission.users - thêm users mới và xóa users không còn
        // Thêm users mới vào mission.users nếu chưa có
        const missionUsersSet = new Set(mission.users.map(u => u.toString()));
        usersToAdd.forEach(userId => missionUsersSet.add(userId));

        // Xóa users khỏi mission.users nếu không còn trong bất kỳ unit nào
        usersToRemove.forEach(userId => {
          const stillInOtherUnits = mission.assignInfo.some(info => 
            info.unit.toString() !== unit.trim() && 
            info.users.some(u => u.toString() === userId)
          );
          if (!stillInOtherUnits) {
            missionUsersSet.delete(userId);
          }
        });

        mission.users = Array.from(missionUsersSet);

        // Cập nhật updatedAt
        mission.updatedAt = Date.now();

        mission.save()
          .then(() => {
            // Populate thông tin để trả về
            return MissionModel.findById(mission._id)
              .populate('createdBy', 'name')
              .populate('assignInfo.unit', 'name')
              .populate('assignInfo.users', 'name phones idNumber avatar')
              .populate('users', 'name phones idNumber avatar');
          })
          .then(updatedMission => {
            // Tìm users mới được thêm vào (có trong newUsers nhưng không có trong currentUsersInUnit)
            const newlyAddedUsers = newUsers.filter(userId => !currentUsersInUnit.includes(userId));
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          })
          .catch(err => next(err));
      })
      .catch(err => next(err));
  }

  const sendNotificationToNewUsers = (updatedMission, newlyAddedUsers, currentUsersInUnit, next) => {
    // Nếu không có users mới được thêm, bỏ qua bước này
    if (!newlyAddedUsers || newlyAddedUsers.length === 0) {
      return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
    }

    // Lấy thông tin chi tiết của các users mới để gửi thông báo
    UserModel.find({ _id: { $in: newlyAddedUsers } })
      .then(newUserDocs => {
        if (!newUserDocs || newUserDocs.length === 0) {
          // Nếu không tìm thấy user docs, vẫn tiếp tục
          return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
        }

        // Tạo nội dung thông báo
        const notificationTitle = `Bạn đã được phân công vào nhiệm vụ: ${updatedMission.name || 'Nhiệm vụ mới'}`;
        const notificationDescription = `Ấn để xem thông tin chi tiết và bắt đầu thực hiện nhiệm vụ.`;
        const notificationData = {
          link: 'MissionDetailScreen',
          extras: {
            _id: updatedMission._id.toString()
          },
          linkWeb: ``
        };

        // Gửi thông báo cho từng user mới
        const notificationPromises = newUserDocs.map(user => {
          return PushNotifyManager.sendToMember(
            user._id,
            notificationTitle,
            notificationDescription,
            notificationData,
            'new_mission'
          )
          .then((res) => {
            console.log(`Sent assignment notification to user ${user.name}`, res);
            return res;
          })
          .catch((error) => {
            console.error(`Failed to send assignment notification to user ${user._id}:`, error);
            // Không throw error để không làm gián đoạn quy trình chính
            return null;
          });
        });

        // Gửi tất cả thông báo
        Promise.allSettled(notificationPromises)
          .then(() => {
            console.log(`Sent mission assignment notifications to ${newUserDocs.length} newly assigned officers`);
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          })
          .catch((err) => {
            // Log error nhưng vẫn tiếp tục
            logger.logError([err], 'sendNotificationToNewUsers', { newlyAddedUsers, missionId: updatedMission._id });
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          });
      })
      .catch(err => {
        // Log error nhưng vẫn tiếp tục
        logger.logError([err], 'sendNotificationToNewUsers - UserModel.find', { newlyAddedUsers });
        next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
      });
  }

  const sendNotificationToRemovedUsers = (updatedMission, newlyAddedUsers, currentUsersInUnit, next) => {
    // Tính toán users bị loại khỏi nhiệm vụ
    const newUsers = users.map(u => u.trim());
    const removedUsers = currentUsersInUnit.filter(userId => !newUsers.includes(userId));
    
    // Nếu không có users bị loại, bỏ qua bước này
    if (!removedUsers || removedUsers.length === 0) {
      return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
    }

    // Lấy thông tin chi tiết của các users bị loại để gửi thông báo
    UserModel.find({ _id: { $in: removedUsers } })
      .then(removedUserDocs => {
        if (!removedUserDocs || removedUserDocs.length === 0) {
          // Nếu không tìm thấy user docs, vẫn tiếp tục
          return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
        }

        // Tạo nội dung thông báo
        const notificationTitle = `Bạn không còn trong nhiệm vụ: ${updatedMission.name || 'Nhiệm vụ'}`;
        const notificationDescription = `Bạn đã được điều chuyển khỏi nhiệm vụ này.`;
        const notificationData = {
          link: 'MainContainer',
          extras: {
            tabIndex: 1,
            _id: updatedMission._id.toString()
          },
          linkWeb: ``
        };

        // Gửi thông báo cho từng user bị loại
        const notificationPromises = removedUserDocs.map(user => {
          return PushNotifyManager.sendToMember(
            user._id,
            notificationTitle,
            notificationDescription,
            notificationData,
            'mission_update'
          )
          .then((res) => {
            console.log(`Sent removal notification to user ${user.name}`, res);
            return res;
          })
          .catch((error) => {
            console.error(`Failed to send removal notification to user ${user._id}:`, error);
            // Không throw error để không làm gián đoạn quy trình chính
            return null;
          });
        });

        // Gửi tất cả thông báo
        Promise.allSettled(notificationPromises)
          .then(() => {
            console.log(`Sent mission removal notifications to ${removedUserDocs.length} removed officers`);
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          })
          .catch((err) => {
            // Log error nhưng vẫn tiếp tục
            logger.logError([err], 'sendNotificationToRemovedUsers', { removedUsers, missionId: updatedMission._id });
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          });
      })
      .catch(err => {
        // Log error nhưng vẫn tiếp tục
        logger.logError([err], 'sendNotificationToRemovedUsers - UserModel.find', { removedUsers });
        next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
      });
  }

  const createMissionLog = (updatedMission, newlyAddedUsers, currentUsersInUnit, next) => {
    const logData = {
      user: req.user?.id || null,
      mission: updatedMission._id,
      message: `Cập nhật danh sách cán bộ`,
      action: 1, 
      data: {
        users,
        unit,
        newlyAddedUsers: newlyAddedUsers || []
      },
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    const missionLog = new MissionLogModel(logData);
    missionLog.save()
      .then(() => {
        next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
      })
      .catch(err => {
        // Nếu ghi log thất bại, vẫn tiếp tục vì mission đã được cập nhật
        logger.logError([err], 'createMissionLog', logData);
        next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
      });
  }

  const notifyMissionCreatorIfTeamLeader = (updatedMission, newlyAddedUsers, currentUsersInUnit, next) => {
    // Kiểm tra xem người cập nhật có phải là team_leader không
    if (req.user.role !== 'team_leader') {
      return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
    }

    // Lấy thông tin người cập nhật với positions để check role
    UserModel.findById(req.user.id)
      .populate('positions', 'isTeamLeader')
      .then(currentUser => {
        if (!currentUser) {
          return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
        }

        // Kiểm tra có phải team_leader không
        const isTeamLeader = currentUser.positions && currentUser.positions.some(pos => pos.isTeamLeader === true);

        if (!isTeamLeader || !updatedMission.createdBy) {
          return next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
        }

        // Lấy thông tin tổ để có tên tổ
        UnitModel.findById(unit.trim())
          .then(unitInfo => {
            const unitName = unitInfo ? unitInfo.name : 'Không xác định';
            const officerName = currentUser.name || 'Không xác định';

            // Tạo nội dung thông báo
            const notificationTitle = `Cập nhật danh sách cán bộ thực hiện nhiệm vụ`;
            const notificationDescription = `Cán bộ ${officerName} đã cập nhật danh sách cán bộ thực hiện nhiệm vụ cho tổ ${unitName}`;
            const notificationData = {
              link: 'MissionDetailScreen',
              extras: {
                _id: updatedMission._id.toString()
              },
              linkWeb: `/manage-mission/${updatedMission._id}`
            };
            // Gửi thông báo cho người tạo mission
            PushNotifyManager.sendToMember(
              updatedMission.createdBy._id || updatedMission.createdBy,
              notificationTitle,
              notificationDescription,
              notificationData,
              'mission_update'
            )
            .then((res) => {
              console.log(`Sent team leader update notification to mission creator`, res);
              next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
            })
            .catch((error) => {
              console.error(`Failed to send team leader update notification:`, error);
              // Không throw error để không làm gián đoạn quy trình chính
              next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
            });
          })
          .catch(err => {
            logger.logError([err], 'notifyMissionCreatorIfTeamLeader - UnitModel.findById', { unit });
            next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
          });
      })
      .catch(err => {
        logger.logError([err], 'notifyMissionCreatorIfTeamLeader - UserModel.findById', { userId: req.user.id });
        next(null, updatedMission, newlyAddedUsers, currentUsersInUnit);
      });
  }

  const updateNotifyAssign = (updatedMission, newlyAddedUsers, currentUsersInUnit, next) => {
    // Lấy notifyAssign từ mission và cập nhật users
    if (!updatedMission.notifyAssign) {
      // Nếu không có notifyAssign, vẫn trả về thành công
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Cập nhật nhiệm vụ thành công'
        },
        data: updatedMission
      });
    }

    SavedNotificationModel.findByIdAndUpdate(
      updatedMission.notifyAssign,
      { 
        users: updatedMission.users,
        updatedAt: Date.now()
      },
      { new: true }
    )
      .then(() => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: req.body.platform === 'web' ? {
            head: 'Thông báo',
            body: 'Cập nhật nhiệm vụ thành công'
          } : null,
          data: updatedMission
        });
      })
      .catch(err => {
        // Nếu cập nhật notification thất bại, vẫn trả về thành công vì mission đã được cập nhật
        logger.logError([err], 'updateNotifyAssign', { notifyAssign: updatedMission.notifyAssign, users: updatedMission.users });
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: req.body.platform === 'web' ? {
            head: 'Thông báo',
            body: 'Cập nhật nhiệm vụ thành công'
          } : null,
          data: updatedMission
        });
      });
  }

  async.waterfall([
    validateParams,
    checkMissionStatus,
    updateMission,
    sendNotificationToNewUsers,
    sendNotificationToRemovedUsers,
    createMissionLog,
    notifyMissionCreatorIfTeamLeader,
    updateNotifyAssign
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body)
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  })
}