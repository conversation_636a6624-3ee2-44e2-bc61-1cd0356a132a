const _ = require('lodash');
const async = require('async');
const config = require('config');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const SavedNotification = require('../../../../models/savedNotification');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  let {
    _id = ''
  } = req.body;

  const id = _id.trim();
  let notification = {};

  const checkParams = (next) => {
    // Kiểm tra ID
    if (!id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getNotification = (next) => {
    SavedNotification
      .findOne({
        _id: id,
        status: { $ne: 0 } // Chỉ lấy notification chưa bị xóa
      })
      .populate('units', 'name description') // Populate thông tin tổ
      .populate('users', 'name phone idNumber avatar') // Populate thông tin user
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }

        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.SYSTEM.DATA_NOT_FOUND
          });
        }

        notification = result;
        next();
      });
  };

  const formatResponse = (next) => {
    // Format notification data
    const formattedNotification = {
      id: notification._id,
      image: notification.image || '',
      title: notification.title,
      description: notification.description || '',
      titlePush: notification.titlePush,
      messagePush: notification.messagePush,
      type: notification.type,
      status: notification.status,
      statusPush: notification.statusPush,
      data: notification.data || {},
      units: notification.units || [],
      users: notification.users || [],
      seen: {
        count: notification.seen ? notification.seen.length : 0,
        userIds: notification.seen || []
      },
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
      
    };

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: formattedNotification
    });
  };


  async.waterfall([
    checkParams,
    getNotification,
    formatResponse
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
      });

    res.json(data || err);
  });
};
