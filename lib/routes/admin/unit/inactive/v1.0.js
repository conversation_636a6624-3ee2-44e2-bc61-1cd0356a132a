const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Unit = require('../../../../models/unit')


module.exports = (req, res) => {
  const idUnit = req.body._id || '';
  let unitInf;
  let updatedData = {};

  const checkParams = (next) => {
    if(!idUnit){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      })
    }
    next();
  }


  const deactiveUnit = (next) => {

    Unit
      .findOneAndUpdate({
        _id: idUnit
      },{
        status: 0
      },{new: true})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        if(!result) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.UNIT.UNIT_NOT_EXISTS
          })
        }
        updatedData = result;
        next(null)
      })
  }

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'inactive_unit',
        description: 'Vô hiệu hóa tổ',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([
    checkParams,
    deactiveUnit,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}