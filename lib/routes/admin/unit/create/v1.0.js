const _ = require('lodash')
const async = require('async')
const ms = require('ms')
const { v4: uuidv4 } = require('uuid');
const config = require('config')
const util = require('util')
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis')
const User = require('../../../../models/user')
const CONSTANTS = require('../../../../const')
const MESSAGES = require('../../../../message')
const Unit = require('../../../../models/unit')
const tool = require('../../../../util/tool')


module.exports = (req, res) => {

  let name = req.body.name || '';
  name = name.trim();
  let icon = req.body.icon || '';
  const parent = req.body.parent || null;
  let parentPath = [];
  let order = 0;
  let updatedData = {};
  const checkParams = (next) => {
    if(!name || !name.trim() ){
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.UNIT.NOT_FOUND_NAME
      })
    }
    next();
  }

  const checkUnitExists = (next) => {

    Unit
      .find({
        name,
        parent,
        status: 1
      })
      .lean()
      .exec((err, results) => {
        if(err) {
          return next(err);
        }
        if(results.length) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.UNIT.UNIT_EXISTS
          })
        }
        next()
      })

  }
  const findParentPath = (next) => {
    if (!parent) {
      return next(); // Nếu không có parent, không cần thực hiện thêm gì
    }
    parentPath.unshift(parent);
    Unit.findById(parent)
      .lean()
      .exec((err, unit) => {
        if (err) return next(err);

        let currentParent = unit.parent;
        const findParents = (currentParent) => {
          return new Promise((resolve, reject) => {
            if (!currentParent) return resolve();

            Unit.findById(currentParent)
              .lean()
              .exec((err, parentUnit) => {
                if (err) return reject(err);

                parentPath.unshift(currentParent);
                resolve(findParents(parentUnit ? parentUnit.parent : null));
              });
          });
        };

        findParents(currentParent)
          .then(() => {
            next(null)
          })
          .catch((err) => {
            next(err);
          });
      });
  };

  const getBiggestOrder = (next) => {
    Unit
      .findOne({parent, status: 1})
      .sort({order: -1})
      .lean()
      .exec((err, result) => {
        if(err) {
          return next(err);
        }
        order = result ? result.order + 1 : req.body.order || 1;
        next();
      })
  }

  const createUnit = (next) => {

    let objCreate = {
      name,
      nameAlias: tool.change_alias(name),
      icon,
      parent,
      parentPath,
      order
    }


    Unit
      .create(objCreate,(err, result) => {
        if(err) {
          return next(err);
        }
        if(name.includes('Cát Bi')) {
          Unit.update({
            _id: result._id,
          },{
            defaultPetitionWard: '6225aba88b114370443f3703'
          },() => {
          })
        }
        if(name.includes('Núi Đối')) {
          Unit.update({
            _id: result._id,
          },{
            defaultPetitionWard: '6225aba88b114370443f3636'
          },() => {
          })
        }
        if(name.includes('Cầu Đất')) {
          Unit.update({
            _id: result._id,
          },{
            defaultPetitionWard: '67734b9e4cfaf83fa0c060e8'
          },() => {
          })
        }
        if(name.includes('Trần Nguyên Hãn')) {
          Unit.update({
            _id: result._id,
          },{
            defaultPetitionWard: '6225aba98b114370443f3805'
          },() => {
          })
        }
        if(name.includes('Thủy Đường')) {
          Unit.update({
            _id: result._id,
          },{
            defaultPetitionWard: '6225aba88b114370443f3678'
          },() => {
          })
        }
        updatedData = result;
        next(null)
      })
  }


  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.UNIT.CREATE_SUCCESS,
      data: updatedData
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'create_unit',
        description: 'Tạo tổ mới',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };


  async.waterfall([
    checkParams,
    checkUnitExists,
    findParentPath,
    getBiggestOrder,
    createUnit,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  })
}