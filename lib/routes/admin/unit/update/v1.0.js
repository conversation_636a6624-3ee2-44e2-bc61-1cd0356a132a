const _ = require('lodash');
const async = require('async');
const ms = require('ms');
const { v4: uuidv4 } = require('uuid');
const config = require('config');
const util = require('util');
const rp = require('request-promise');

const redisConnection = require('../../../../connections/redis');
const User = require('../../../../models/user');
const CONSTANTS = require('../../../../const');
const MESSAGES = require('../../../../message');
const Unit = require('../../../../models/unit');
const Permission = require('../../../../models/permission');
const GroupPermission = require('../../../../models/groupPermission');
const tool = require('../../../../util/tool');

module.exports = (req, res) => {
  let name = req.body.name || '';
  let icon = req.body.icon || '';
  const idUnit = req.body._id || '';

  let unitInf;
  let updatedData = {};

  const checkParams = (next) => {
    if (!idUnit) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS,
      });
    }

    if (!name) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.UNIT.NOT_FOUND_NAME,
      });
    }

    next();
  };

  const checkUnitExists = (next) => {
    Unit.findById(idUnit)
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (!result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.UNIT.UNIT_NOT_EXISTS,
          });
        }
        unitInf = result;
        next();
      });
  };

  const checkNameNotExists = (next) => {
    Unit.findOne({
      name: name.trim(),
      parent: unitInf.parent,
      _id: {
        $ne: idUnit,
      },
      status: 1,
    })
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        if (result) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: MESSAGES.UNIT.UNIT_EXISTS,
          });
        }
        next();
      });
  };

  const updateUnite = (next) => {
    let obj = {
      updatedAt: Date.now(),
      name: name.trim(),
      nameAlias: tool.change_alias(name.trim()),
      icon,
    };

    Unit.findOneAndUpdate(
      {
        _id: idUnit,
      },
      obj,
      {
        new: true,
      }
    )
      .lean()
      .exec((err, result) => {
        if (err) {
          return next(err);
        }
        updatedData = result;
        next(null);
      });
  };

  const writeLog = (next) => {
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      message: MESSAGES.UNIT.UPDATE_SUCCESS,
    });

    SystemLogModel.create(
      {
        user: _.get(req,'user.id', ''),
        action: 'update_unit',
        description: 'Cập nhật tổ',
        data: req.body,
        updatedData,
      },
      () => {}
    );
  };

  async.waterfall([checkParams, checkUnitExists, checkNameNotExists, updateUnite, writeLog], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
