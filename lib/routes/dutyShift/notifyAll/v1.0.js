const _ = require('lodash');
const async = require('async');
const StatisticsTrigger = require('../../../utils/statisticsTrigger');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const DutySpecializedScheduleModel = require('../../../models/dutySpecializedSchedule');
const DutyCriminalScheduleModel = require('../../../models/dutyCriminalSchedule');
const DutyMainScheduleModel = require('../../../models/dutyMainSchedule');
const DutySubScheduleModel = require('../../../models/dutySubSchedule');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');
const DutyPatrolScheduleModel = require('../../../models/dutyPatrolSchedule');
const DutyStadiumScheduleModel = require('../../../models/dutyStadiumSchedule');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const PushNotifyManager = require('../../../jobs/pushNotify');
const SavedNotificationModel = require('../../../models/savedNotification');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const _id = _.get(req, 'body._id', '');
  const type = _.get(req, 'body.type', '');
  const users = [];
  let model;
  const typeArr = [
    'dutySpecializedSchedule',
    'dutyCriminalSchedule',
    'dutyMainSchedule',
    'dutySubSchedule',
    'dutyLocationSchedule',
    'dutyPatrolSchedule',
    'dutyStadiumSchedule',
    'dutyEmergencySchedule'
  ]
  let dutyName = '';

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    if(typeArr.indexOf(type) === -1) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp loại lịch hợp lệ'
        }
      });
    }
    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực'
        }
      });
    }
    if(type === 'dutySpecializedSchedule') {
      model = DutySpecializedScheduleModel;
    } else if(type === 'dutyCriminalSchedule') {
      model = DutyCriminalScheduleModel;
    } else if(type === 'dutyMainSchedule') {
      model = DutyMainScheduleModel;
    } else if(type === 'dutySubSchedule') {
      model = DutySubScheduleModel;
    } else if(type === 'dutyLocationSchedule') {
      model = DutyLocationScheduleModel;
    } else if(type === 'dutyPatrolSchedule') {
      model = DutyPatrolScheduleModel;
    } else if(type === 'dutyStadiumSchedule') {
      model = DutyStadiumScheduleModel;
    } else if(type === 'dutyEmergencySchedule') {
      model = DutyEmergencyScheduleModel;
    }
    next();
  };

  const findShift = (next) => {
    let objFind = {
      status: 1
    }
    objFind[`${type}`] = _id;

    DutyShiftModel
      .find(objFind)
      .lean()
      .exec((err, shifts) => {
        if (err) {
          return next(err);
        }

        if (!shifts || shifts.length === 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy ca trực liên quan đến lịch trực này'
            }
          });
        }
        // Lấy danh sách người dùng từ các ca trực
        shifts.forEach(shift => {
          if (shift.officer && shift.officer._id && !users.includes(shift.officer._id.toString())) {
            users.push(shift.officer._id.toString());
          }
        });
        next();
      })
  }

  // Tìm scheduleDoc
  const findScheduleDoc = (next) => {
    model.findById(_id, (err, scheduleDoc) => {
      if (err || !scheduleDoc) {
        return next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR
        });
      }
      next(null, scheduleDoc);
    });
  };

  // Tạo hoặc cập nhật SavedNotificationModel
  const saveOrUpdateNotification = (scheduleDoc, next) => {
    if (users.length === 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không có cán bộ nào liên quan đến lịch trực này'
        }
      });
    }
    const title = 'Thông báo lịch trực';
    const description = `Cán bộ có lịch trực mới. Vui lòng kiểm tra ứng dụng để biết thêm chi tiết.`;
    const data = {
      link: 'MainContainer',
      extras: {
        tabIndex: 1
      },
      linkWeb: `/my-work-schedule`
    };
    let savedNotificationId = scheduleDoc && scheduleDoc.savedNotification;
    if (savedNotificationId) {
      SavedNotificationModel.findByIdAndUpdate(
        savedNotificationId,
        {
          title,
          description,
          users,
          type:'user',
          data,
          updatedAt: Date.now()
        },
        { new: true },
        (err, savedNotificationDoc) => {
          if (err) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          next(null, scheduleDoc, title, description, data);
        }
      );
    } else {
      SavedNotificationModel.create({
        title,
        description,
        users,
        type:'user',
        data,
        createdAt:  Date.now(),
        updatedAt:  Date.now()
      }, (err, savedNotificationDoc) => {
        if (err) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.ERROR
          });
        }
        scheduleDoc.savedNotification = savedNotificationDoc._id;
        scheduleDoc.save((err) => {
          if (err) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR
            });
          }
          next(null, scheduleDoc, title, description, data);
        });
      });
    }
  };

  // Push notify
  const pushNotify = (scheduleDoc, title, description, data, next) => {
    const promises = users.map(userId => {
      return PushNotifyManager.sendToMember(userId, title, description, data, 'job_update', 'ioc');
    });
    
    Promise.all(promises)
      .then((results) => {
        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.SYSTEM.SUCCESS,
          data: results
        });
      })
      .catch(err => {
        next({
          code: CONSTANTS.CODE.SUCCESS,
          message: MESSAGES.SYSTEM.SUCCESS,
        });
      });
  };

  async.waterfall([
    checkParams,
    findShift,
    findScheduleDoc,
    saveOrUpdateNotification,
    pushNotify
  ], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });
    res.json(data || err);
  });
};
