const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyLocationScheduleModel = require('../../../models/dutyLocationSchedule');
const DutyShiftTemplateModel = require('../../../models/dutyShiftTemplate');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  let { startDate, location } = req.body; // startDate: timestamp của ngày bắt đầu tháng
  let targetDate;

  // Nếu không truyền startDate, tự động tính toán từ thời gian hiện tại
  if (!startDate) {
    const currentDate = new Date();
    startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).getTime();
  } else {
    // Nếu có truyền startDate, vẫn tính lại thành ngày bắt đầu của tháng
    const inputDate = new Date(startDate);
    startDate = new Date(inputDate.getFullYear(), inputDate.getMonth(), 1).getTime();
  }

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }
    
    // Validate startDate is a valid timestamp (sau khi đã tự động tính toán nếu cần)
    if (isNaN(startDate) || startDate <= 0) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Ngày bắt đầu tháng không hợp lệ'
        }
      });
    }
    
    if (!location) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp địa điểm'
        }
      });
    }
    
    next();
  };

  const getdutyLocationSchedule = (next) => {
    // Sử dụng startDate làm ngày bắt đầu tháng
    targetDate = new Date(startDate);
    
    // Tính toán ngày cuối tháng
    const endOfMonth = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);
    endOfMonth.setHours(23, 59, 59, 999);

    // Tìm bản ghi có status = 1 và thời gian target nằm trong khoảng startTime và endTime
    DutyLocationScheduleModel.findOne({
      status: 1,
      location: location, 
      startTime: { $lte: endOfMonth.getTime() },
      endTime: { $gte: targetDate.getTime() + 1}
    })
    .populate({
      path: 'weeklySchedule',
      select: 'name startTime endTime officer forLeader unit',
      populate: {
        path: 'officer',
        select: 'name avatar phones idNumber',
      }
    })
    .populate({
      path: 'weeklyScheduleTemplate.data.unit',
      select: 'name'
    })
    .lean()
    .exec((err, schedule) => {
      if (err) {
        return next(err);
      }

      if (schedule) {
        // Nếu tìm thấy bản ghi, trả về
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          data: schedule
        });
      }

      // Nếu không tìm thấy, tạo mới bản ghi mặc định
      next(null, null);
    });
  };

  const createDefaultSchedule = (result, next) => {
    if (result) {
      return next(null, result);
    }

    // Lấy template mặc định từ dutyShiftTemplate
    DutyShiftTemplateModel.findOne({
      status: 1,
      source: 'location',
      location: location
    })
    .lean()
    .exec((err, template) => {
      if (err) {
        return next(err);
      }

      let defaultMonthlyTemplate = [];
      
      // Tính thời gian đầu tháng và cuối tháng
      const monthStartTime = new Date(targetDate);
      monthStartTime.setHours(0, 0, 0, 0);
      
      const monthEndTime = new Date(targetDate.getFullYear(), targetDate.getMonth() + 1, 0);
      monthEndTime.setHours(23, 59, 59, 999);
      
      const daysInMonth = monthEndTime.getDate();

      if (template && template.template) {
        // Tạo lịch cho từng ngày trong tháng dựa trên template
        for (let day = 1; day <= daysInMonth; day++) {
          const currentDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), day);
          const dayOfWeek = currentDay.getDay(); // 0: Chủ nhật, 1: Thứ 2, ..., 6: Thứ 7
          const dayStartTime = currentDay.getTime();
          
          // Lấy template theo ngày trong tuần
          const dayTemplate = template.template[dayOfWeek === 0 ? 6 : dayOfWeek - 1]; // Chuyển đổi: CN=6, T2=0, T3=1...
          
          const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
          
          defaultMonthlyTemplate.push({
            name: dayNames[dayOfWeek],
            date: `${day}/${targetDate.getMonth() + 1}`,
            dayStartTime: dayStartTime,
            data: dayTemplate.data.map(shift => ({
              forLeader: shift.forLeader || false,
              unit: shift.unit || null,
              startTime: dayStartTime + shift.startTime,
              endTime: dayStartTime + shift.endTime,
              unitSub: 'Lực lượng Anh ninh cơ sở'
            }))
          });
        }
      } else {
        // Fallback template nếu không tìm thấy - tạo lịch trống cho từng ngày
        for (let day = 1; day <= daysInMonth; day++) {
          const currentDay = new Date(targetDate.getFullYear(), targetDate.getMonth(), day);
          const dayOfWeek = currentDay.getDay();
          const dayStartTime = currentDay.getTime();
          
          const dayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
          
          defaultMonthlyTemplate.push({
            name: dayNames[dayOfWeek],
            date: `${day}/${targetDate.getMonth() + 1}`,
            dayStartTime: dayStartTime,
            data: []
          });
        }
      }
      
      const defaultSchedule = new DutyLocationScheduleModel({
        name: 'Lịch chốt điểm',
        description: 'Lịch chốt điểm cho các cán bộ',
        startTime: monthStartTime.getTime(),
        endTime: monthEndTime.getTime() + 1,
        dutyType: 'location',
        dutyName: `Trực chốt ${location}`,
        location: location,
        weeklyScheduleTemplate: defaultMonthlyTemplate,
        weeklySchedule: [],
        status: 1,
        createdBy: userId
      });

      defaultSchedule.save((err, savedSchedule) => {
        if (err) {
          return next(err);
        }

        // Populate thông tin unit sau khi save để trả về đầy đủ thông tin
        DutyLocationScheduleModel.findById(savedSchedule._id)
          .populate({
            path: 'weeklySchedule',
            select: 'name startTime endTime officer forLeader unit',
            populate: {
              path: 'officer',
              select: 'name avatar phones idNumber',
            }
          })
          .populate({
            path: 'weeklyScheduleTemplate.data.unit',
            select: 'name'
          })
          .lean()
          .exec((err, populatedSchedule) => {
            if (err) {
              return next(err);
            }

            next(null, {
              code: CONSTANTS.CODE.SUCCESS,
              data: populatedSchedule
            });
          });
      });
    });
  };

  async.waterfall([checkParams, getdutyLocationSchedule, createDefaultSchedule], (err, data) => {
    if (_.isError(err)) {
      logger.logError([err], req.originalUrl, req.body);
      MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }
    if (err && _.isError(err)) {
      data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      };
    }

    res.json(data || err);
  });
};
