const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Jo<PERSON>);

const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API xóa (inactive) bản cập nhật theo dõi
 * POST /api/v1.0/criminal-subject-update/inactive
 */
module.exports = (req, res) => {
  const { updateId } = req.body;

  let existingUpdate;

  // Validation schema
  const schema = Joi.object({
    updateId: Joi.objectId().required()
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const checkUpdateExists = (next) => {
    CriminalSubjectUpdateModel.findOne({
      _id: updateId,
      status: 1
    }, 'subjectId name').lean().exec((err, update) => {
      if (err) {
        return next(err);
      }

      if (!update) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.UPDATE_NOT_FOUND
        });
      }

      existingUpdate = update;
      next();
    });
  };

  const inactiveUpdate = (next) => {
    CriminalSubjectUpdateModel.updateOne(
      { _id: updateId },
      {
        $set: {
          status: 0,
          deletedAt: Date.now()
        }
      }
    ).exec((err, result) => {
      if (err) {
        return next(err);
      }

      if (result.modifiedCount === 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: MESSAGES.CRIMINAL_SUBJECT.UPDATE_NOT_FOUND
        });
      }

      next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: MESSAGES.CRIMINAL_SUBJECT.DELETE_UPDATE_SUCCESS,
        data: {
          updateId: updateId,
          name: existingUpdate.name,
          deletedAt: Date.now()
        }
      });
    });
  };

  const writeLog = (data, next) => {
    next(null, data);

    try {
      SystemLogModel && SystemLogModel.create({
        user: _.get(req, 'user.id', ''),
        action: 'inactive_criminal_subject_update',
        description: 'Xóa (inactive) bản cập nhật theo dõi',
        data: req.body,
        updatedData: _.get(data, 'data')
      }, () => { });
    } catch (e) { }
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    checkUpdateExists,
    inactiveUpdate,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
