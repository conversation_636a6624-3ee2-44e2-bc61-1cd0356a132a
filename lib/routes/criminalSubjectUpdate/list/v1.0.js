const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
Joi.objectId = require('joi-objectid')(Joi);

const CriminalSubjectUpdateModel = require('../../../models/criminalSubjectUpdate');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');

/**
 * API lấy danh sách cập nhật theo dõi đối tượng hình sự
 * POST /api/v1.0/criminal-subject-update/list
 */
module.exports = (req, res) => {
  const {
    page = 1,
    limit = 20,
    subjectId,
    updatedBy,
    contactDateFrom,
    contactDateTo,
    createdFrom,
    createdTo,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.body;

  let updates = [];
  let totalCount = 0;
  let searchQuery = { status: 1 };

  // Validation schema
  const schema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    subjectId: Joi.objectId(),
    updatedBy: Joi.objectId(),
    contactDateFrom: Joi.number().integer(),
    contactDateTo: Joi.number().integer(),
    createdFrom: Joi.number().integer(),
    createdTo: Joi.number().integer(),
    sortBy: Joi.string().valid('contactDate', 'createdAt', 'updatedBy').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  });

  const validateParams = (next) => {
    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const buildSearchQuery = (next) => {
    // Filter by subject
    if (subjectId) {
      searchQuery.subjectId = subjectId;
    }

    // Filter by updater
    if (updatedBy) {
      searchQuery.updatedBy = updatedBy;
    }

    // Contact date range filter
    if (contactDateFrom || contactDateTo) {
      searchQuery.contactDate = {};
      if (contactDateFrom) searchQuery.contactDate.$gte = contactDateFrom;
      if (contactDateTo) searchQuery.contactDate.$lte = contactDateTo;
    }

    // Created date range filter
    if (createdFrom || createdTo) {
      searchQuery.createdAt = {};
      if (createdFrom) searchQuery.createdAt.$gte = createdFrom;
      if (createdTo) searchQuery.createdAt.$lte = createdTo;
    }

    next();
  };

  const getUpdates = (next) => {
    const skip = (page - 1) * limit;
    const sortObj = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    CriminalSubjectUpdateModel.find(searchQuery)
      .select('-status')
      .populate('subjectId', 'name idNumber category dangerLevel')
      .populate({
        path: 'updatedBy',
        select: 'name idNumber avatar units positions',
        populate: [{
          path: 'units',
          select: 'name'
        }, {
          path: 'positions',
          select: 'name'
        }]
      })
      .sort(sortObj)
      .skip(skip)
      .limit(parseInt(limit))
      .lean()
      .exec((err, results) => {
        if (err) {
          return next(err);
        }

        updates = results;
        next();
      });
  };

  const getTotalCount = (next) => {
    CriminalSubjectUpdateModel.countDocuments(searchQuery, (err, count) => {
      if (err) {
        return next(err);
      }

      totalCount = count;
      next();
    });
  };

  const formatResponse = (next) => {
    const totalPages = Math.ceil(totalCount / limit);

    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        updates: updates,
        pagination: {
          currentPage: parseInt(page),
          totalPages: totalPages,
          totalItems: totalCount,
          itemsPerPage: parseInt(limit),
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1
        },
        filters: {
          subjectId,
          updatedBy,
          contactDateFrom,
          contactDateTo,
          createdFrom,
          createdTo
        },
        sorting: {
          sortBy,
          sortOrder
        }
      }
    });
  };

  const writeLog = (data, next) => {
    try {
      SystemLogModel && SystemLogModel.create({
        user: _.get(req, 'user.id', ''),
        action: 'list_criminal_subject_update',
        description: 'Lấy danh sách cập nhật theo dõi đối tượng hình sự',
        data: req.body,
        updatedData: _.get(data, 'data')
      }, () => { });
    } catch (e) { }
    next(null, data);
  };

  // Execute waterfall
  async.waterfall([
    validateParams,
    buildSearchQuery,
    getUpdates,
    getTotalCount,
    formatResponse,
    writeLog
  ], (err, data) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Handle system errors
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
