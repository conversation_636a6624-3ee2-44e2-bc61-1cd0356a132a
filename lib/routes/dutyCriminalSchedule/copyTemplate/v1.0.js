
const _ = require('lodash');
const async = require('async');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyCriminalScheduleModel = require('../../../models/dutyCriminalSchedule');


module.exports = async (req, res) => {
  try {
    const userId = _.get(req, 'user.id');
    const { _id } = req.body;
    if (!userId || !_id) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON>ui lòng cung cấp ID lịch trực và đăng nhập!'
        }
      });
    }

    // B1: <PERSON><PERSON><PERSON> lịch trực hiện tại
    const currentSchedule = await DutyCriminalScheduleModel.findOne({ _id, status: 1 }).lean();
    if (!currentSchedule) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: '<PERSON><PERSON><PERSON> trực không tồn tại hoặc đã bị xóa'
        }
      });
    }
    const { startTime, endTime } = currentSchedule;
    if (!startTime || !endTime) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Lịch trực hiện tại thiếu thông tin thời gian'
        }
      });
    }

    // B2: Tính toán tuần trước
    const prevStartTime = startTime - 7 * 24 * 60 * 60 * 1000;
    const prevEndTime = endTime - 7 * 24 * 60 * 60 * 1000;

    // B3: Lấy lịch trực tuần trước
    const prevSchedule = await DutyCriminalScheduleModel.findOne({
      status: 1,
      startTime: prevStartTime,
      endTime: prevEndTime
    }).lean();
    if (!prevSchedule || !prevSchedule.weeklyScheduleTemplate) {
      return res.json({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Không tìm thấy mẫu lịch trực tuần trước!'
        }
      });
    }

    // B4: Biến đổi weeklyScheduleTemplate sang tuần này
    const newWeeklyScheduleTemplate = prevSchedule.weeklyScheduleTemplate.map(item => {
      const newItem = { ...item };
      if (newItem.dayStartTime) newItem.dayStartTime += 7 * 24 * 60 * 60 * 1000;
      if (Array.isArray(newItem.data)) {
        newItem.data = newItem.data.map(d => ({
          ...d,
          startTime: d.startTime ? d.startTime + 7 * 24 * 60 * 60 * 1000 : d.startTime,
          endTime: d.endTime ? d.endTime + 7 * 24 * 60 * 60 * 1000 : d.endTime
        }));
      } else if (newItem.data && typeof newItem.data === 'object') {
        if (newItem.data.startTime) newItem.data.startTime += 7 * 24 * 60 * 60 * 1000;
        if (newItem.data.endTime) newItem.data.endTime += 7 * 24 * 60 * 60 * 1000;
      }
      return newItem;
    });

    // B5: Update vào DB
    const updated = await DutyCriminalScheduleModel.findOneAndUpdate(
      { _id, status: 1 },
      {
        $set: {
          weeklyScheduleTemplate: newWeeklyScheduleTemplate,
          updatedAt: Date.now()
        }
      },
      { new: true }
    )
    .populate({
      path: 'weeklySchedule',
      select: 'name startTime endTime officer',
      populate: {
        path: 'officer',
        select: 'name avatar phones idNumber',
      }
    })
    .lean();

    return res.json({
      code: CONSTANTS.CODE.SUCCESS,
      message: {
        head: 'Thông báo',
        body: 'Copy mẫu lịch trực từ tuần trước thành công!'
      },
      data: updated
    });
  } catch (err) {
    return res.json({
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR,
      error: err?.message
    });
  }
};
