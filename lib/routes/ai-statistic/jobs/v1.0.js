const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const config = require('config');
const fetch = require('node-fetch');
const UnitModel = require('../../../models/unit');
const { Configuration, OpenAIApi } = require("openai");

const configuration = new Configuration({
  apiKey: config.ai.apiKey,
  basePath: config.ai.url,
  baseOptions: {
    headers: {
      'Authorization': `Bearer ${config.ai.apiKey}`,
    },
  },
});

const client = new OpenAIApi(configuration);


module.exports = (req, res) => {
  let {
    type = 'week',
    endTime,
    unit = '',
    unitIds = [],
    data={}
  } = req.body;

  unitName = '';
  let statisticsData = {

   
  };
  let aiResponse = {};

  const validateParams = (next) => {
    const options = ['3days', '7days', 'week', 'month', 'year'];
    if (type && !options.includes(type)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Lỗi tham số',
          body: `Tham số type không hợp lệ. Các giá trị hợp lệ là: ${options.join(', ')}`
        }
      });
    }
    if (!endTime) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,    
        message: {
          head: 'Lỗi tham số',
          body: 'Tham số endTime là bắt buộc'
        }
      });
    }

    

    // Tính toán startTime dựa theo endTime và type
    const endDate = new Date(endTime);
    
    switch (type) {
      case '3days':
        strategyText = '3 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 2);
        break;
      case '7days':
        strategyText = '7 ngày gần nhất';
        startTime = new Date(endDate);
        startTime.setDate(endDate.getDate() - 6);
        break;
      case 'week':
        strategyText = 'tuần hiện tại';
        startTime = new Date(endDate);
        // Tính ngày Thứ 2 của tuần (1 = Monday)
        const dayOfWeek = endDate.getDay(); // 0=Sunday, 1=Monday, ..., 6=Saturday
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Nếu là CN thì lùi 6 ngày, ngược lại lùi (dayOfWeek - 1) ngày
        startTime.setDate(endDate.getDate() - daysToMonday);
        break;
      case 'month':
        strategyText = 'của các tuần trong tháng hiện tại';
        startTime = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
        break;
      case 'year':
        strategyText = 'của các tháng trong năm nay';
        startTime = new Date(endDate.getFullYear(), 0, 1);
        break;
      default:
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Lỗi tham số',
            body: `Tham số type không hợp lệ`
          }
        });
    }

    // Tạo timeText từ startTime và endTime
    const formatDate = (date) => {
      const day = String(date.getDate()).padStart(2, '0');
      const month = String(date.getMonth() + 1).padStart(2, '0');
      return `${day}/${month}`;
    };
    
    timeText = `Từ ${formatDate(startTime)} đến ${formatDate(endDate)}`;
    
    next();
  };
  const getUnitName = (next) => {
      if (!unit) return next(null);
      UnitModel
        .findOne({ _id: unit, status: 1 })
        .select('name')
        .lean()
        .exec((err, unitData) => {
          if (err) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: MESSAGES.SYSTEM.ERROR,
              error: err
            });
          }
          if (!unitData) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Lỗi tham số',
                body: 'Tổ không tồn tại'
              }
            });
          }
          unitName = unitData.name;
          next(null);
        });
    }
  const getStatisticsData = (next) => {
    // Chuẩn hóa dữ liệu sang tiếng Việt
    if (data && typeof data === 'object') {
      // Đổi tên các trường sang tiếng Việt
      const viSummary = {
        'Tổng số cán bộ': data.summary?.totalUsers || 0,
        'Tổng số ca điểm danh': data.summary?.total || 0,
        'Đúng giờ': data.summary?.onTime || 0,
        'Muộn': data.summary?.late || 0,
        'Không điểm danh': data.summary?.absent || 0,
        'Nghỉ phép': data.summary?.excused || 0,
        'Công tác': data.summary?.businessTrip || 0,
        'Chưa điểm danh': data.summary?.nonAttendance || 0
      };

      const viMetrics = Array.isArray(data.metrics) ? data.metrics.map(item => ({
        'Nhãn thời gian': item.timeLabel,
        'Ngày': `${item.period?.day || ''}/${item.period?.month || ''}/${item.period?.year || ''}`,
        'Tổng số cán bộ': item.summary?.totalUsers || 0,
        'Tổng số ca điểm danh': item.summary?.total || 0,
        'Đúng giờ': item.summary?.onTime || 0,
        'Muộn': item.summary?.late || 0,
        'Không điểm danh': item.summary?.absent || 0,
        'Nghỉ phép': item.summary?.excused || 0,
        'Công tác': item.summary?.businessTrip || 0,
        'Chưa điểm danh': item.summary?.nonAttendance || 0
      })) : [];

      const viTopAbsentUsers = Array.isArray(data.topAbsentUsers) ? data.topAbsentUsers.map(u => ({
        'Tên cán bộ': u.name,
        'Số hiệu': u.idNumber,
        'Số lần Không điểm danh': u.count
      })) : [];

      const viTopLateUsers = Array.isArray(data.topLateUsers) ? data.topLateUsers.map(u => ({
        'Tên cán bộ': u.name,
        'Số hiệu': u.idNumber,
        'Số lần đi muộn': u.count
      })) : [];

      const viTopOnTimeUsers = Array.isArray(data.topOnTimeUsers) ? data.topOnTimeUsers.map(u => ({
        'Tên cán bộ': u.name,
        'Số hiệu': u.idNumber,
        'Số lần đúng giờ': u.count
      })) : [];

      statisticsData = {
        'Tổng hợp': viSummary,
        'Thống kê theo ngày': viMetrics,
        'Top Không điểm danh': viTopAbsentUsers,
        'Top đi muộn': viTopLateUsers,
        'Top đúng giờ': viTopOnTimeUsers
      };
    }
    next();
  };


  const callAIStream = (next) => {
    const contextPrompt = `
Dữ liệu đầu vào là số liệu thống kê tình trạng cán bộ ${unitName ? `thuộc ${unitName}` : ''} đi làm ${strategyText} ${timeText}.  

Dữ liệu:
${JSON.stringify(statisticsData, null, 2)}

Nhiệm vụ:
1) Tổng quan & KPI
   - Tính tỷ lệ đúng giờ, muộn, chưa điểm danh, nghỉ phép/công tác trên tổng lượt chấm.
   - So sánh với ngưỡng SLA (ví dụ: đúng giờ ≥ 90%, muộn ≤ 5%). Đánh giá đạt/không đạt.

2) Xu hướng theo ngày/tuần/tháng
   - Nêu ngày cao điểm đi muộn/không điểm danh, ngày tốt nhất.
   - Chỉ ra biến động (tăng/giảm) so với trung bình tuần và độ lệch chuẩn.

3) Phân tích theo cá nhân/tổ
   - Liệt kê top cán bộ đi muộn nhiều nhất, top chưa điểm danh nhiều nhất, top đúng giờ nhiều nhất.
   - Phân loại: tái phạm (≥ X lần/tuần) vs. phát sinh ngẫu nhiên.
   - Nếu có cán bộ liên tục tái phạm đi muộn, đánh dấu cảnh báo.

4) Nguyên nhân khả dĩ & tác động
   - Giả thuyết theo khung giờ/ca trực/khu vực/loại nhiệm vụ.
   - Tác động đến trực ban, điều động lực lượng.

5) Khuyến nghị & cảnh báo tự động
   - Biện pháp ngắn hạn, trung hạn, dài hạn (nhắc nhở, xác thực kép, tự động hóa quy trình).
   - Đặt ngưỡng cảnh báo (ví dụ: cá nhân muộn ≥ 3 lần/tuần; ngày có muộn > trung bình + 1σ).

Trình bày ngắn gọn, rõ ràng, có số liệu minh chứng và gạch đầu dòng
Trả lời hoàn toan bằng tiếng Việt.`;

    const url = `${config.ai.url}/chat/completions`;
    const headers = {
      'Authorization': `Bearer ${config.ai.apiKey}`,
      'Content-Type': 'application/json'
    };
    const body = JSON.stringify({
      model: config.ai.model,
      messages: [
        { role: "system", content: "Bạn là chuyên gia phân tích kỷ luật điểm danh cho hệ thống IOC Công an." },
        { role: "user", content: contextPrompt }
      ],
      max_tokens: 60000,
      temperature: 0.2,
      stream: true
    });

    fetch(url, { method: 'POST', headers, body })
      .then(response => {
        if (!response.ok || !response.body) {
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Không thể stream từ OpenAI'
          });
        }

        res.setHeader('Content-Type', 'text/event-stream; charset=utf-8');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        let fullContent = '';
        response.body.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          lines.forEach(line => {
            if (line.startsWith('data:')) {
              const data = line.replace('data: ', '').trim();
              if (data === '[DONE]') {
                res.end();
              } else {
                try {
                  const json = JSON.parse(data);
                  const delta = json.choices?.[0]?.delta?.content || '';
                  console.log('Delta received:', delta);
                  if (delta) {
                    fullContent += delta;
                    res.write(delta);
                  }
                } catch (e) {}
              }
            }
          });
        });
        response.body.on('end', () => {
          console.log('Full AI Response:', fullContent);
          aiResponse = { content: fullContent };
          next();
        });
        response.body.on('error', (err) => {
          next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: 'Lỗi stream OpenAI',
            error: err
          });
        });
      })
      .catch(error => {
        next({
          code: CONSTANTS.CODE.SYSTEM_ERROR,
          message: MESSAGES.SYSTEM.ERROR,
          error
        });
      });
  };

  const returnResult = (next) => {  
    next(null, {
      code: CONSTANTS.CODE.SUCCESS,
      data: {
        report: aiResponse.content,
        metadata: {
          type,
          unitIds,
          statisticsData,
          aiModel: aiResponse.model,
          usage: aiResponse.usage,
          generatedAt: Date.now()
        }
      }
    });
  };

  async.waterfall([
    validateParams,
    getUnitName,
    getStatisticsData,
    callAIStream,
    returnResult
  ], (err, data) => {
    if (_.isError(err)) {
      global.logger && global.logger.logError([err], req.originalUrl, req.body);
      global.MailUtil && global.MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Nếu đã trả về qua stream thì không cần res.json nữa
    if (!res.headersSent) {
      err && _.isError(err) && (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR
      });
      res.json(data || err);
    }
  });
};
