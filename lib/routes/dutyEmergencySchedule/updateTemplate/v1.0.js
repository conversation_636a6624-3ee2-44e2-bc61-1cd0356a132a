const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const UnitModel = require('../../../models/unit');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, data } = req.body; // _id: ID của schedule, templateId: ID của phần tử trong weeklyScheduleTemplate, data: array [{unit, requiredOfficer}]
  let startTimeRemove = []
  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực đột xuất'
        }
      });
    }

    if (!mongoose.Types.ObjectId.isValid(_id)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID lịch trực đột xuất không hợp lệ'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    if (!mongoose.Types.ObjectId.isValid(templateId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID phần tử template không hợp lệ'
        }
      });
    }


    // Validate từng phần tử trong data
    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      
      if (!item.unit) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Phần tử ${i + 1}: Vui lòng cung cấp ID của Tổ công tác`
          }
        });
      }

      // Validate ObjectId format
      if (!mongoose.Types.ObjectId.isValid(item.unit)) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Phần tử ${i + 1}: ID Tổ công tác không hợp lệ`
          }
        });
      }

      if (typeof item.requiredOfficer !== 'number' || item.requiredOfficer < 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `Phần tử ${i + 1}: Số cán bộ yêu cầu phải là số >= 0`
          }
        });
      }
    }

    next();
  };

  const validateMaxOfficer = (next) => {
    // Validate từng unit trong data
    async.eachSeries(data, (item, callback) => {
      // Lấy thông tin unit và đếm số lượng user thuộc unit
      async.parallel({
        unit: (cb) => {
          UnitModel.findById(item.unit).select('name').lean().exec(cb);
        },
        userCount: (cb) => {
          UserModel.countDocuments({
            units: new mongoose.Types.ObjectId(item.unit),
            status: 1
          }, cb);
        }
      }, (err, results) => {
        if (err) {
          return callback(err);
        }

        const unitName = results.unit ? results.unit.name : item.unit;
        const maxOfficer = results.userCount;

        if (item.requiredOfficer > maxOfficer) {
          return callback({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: `"${unitName}": Số lượng cán bộ yêu cầu (${item.requiredOfficer}) không thể vượt quá số lượng cán bộ tối đa của tổ công tác (${maxOfficer})`
            }
          });
        }

        callback();
      });
    }, (err) => {
      if (err) {
        return next(err);
      }
      next();
    });
  };



  const updateWeeklyScheduleTemplate = (next) => {
    // Lấy dữ liệu hiện tại
    DutyEmergencyScheduleModel.findOne({ _id: _id, status: 1 })
      .populate('weeklySchedule', 'startTime endTime')
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'lịch trực đột xuất không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tạo bản sao của weeklyScheduleTemplate hiện tại
        let updatedTemplate = [...(schedule.weeklyScheduleTemplate || [])];

        // Tìm và cập nhật phần tử có templateId khớp
        const index = updatedTemplate.findIndex(item => item._id.toString() === templateId);
        if (index === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy tổ công tác đã cung cấp'
            }
          });
        }

        // Thay thế hoàn toàn data trong template
        const currentItem = updatedTemplate[index];
        let startTimeDiffs = []
        let startTimeOld = []
        data.map(item => {
          if(!item._id) {
            if(!startTimeDiffs.includes(item.startTime)) {
              startTimeDiffs.push(item.startTime)
            }
          } else {
            if(!startTimeOld.includes(item.startTime)) {
              startTimeOld.push(item.startTime)
            }
          }
        })
        let startTimeCurrent = []
        currentItem.data.map(item => {
          if(!startTimeCurrent.includes(item.startTime)) {
            startTimeCurrent.push(item.startTime)
          }
        })
        startTimeCurrent.map(item => {
          if(!startTimeOld.includes(item) && !startTimeDiffs.includes(item)) {
            startTimeRemove.push(item)
          }
        })
        if(currentItem.status && currentItem.status !== 0) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực đột xuất đã kết thúc, Cán bộ không thể thay đổi lịch trực'
            }
          });
        }
        let weeklyScheduleUpdate = [];
        schedule.weeklySchedule.map(shift => {
          if(!startTimeRemove.includes(shift.startTime)) {
            weeklyScheduleUpdate.push(shift._id)
          } else {
            console.log('Set status shift to 2 (inactive): ', shift._id);
            DutyShiftModel.findOneAndUpdate({ _id: shift._id }, { $set: { status: 2, updatedAt: Date.now() } }).exec();
          }
        })

        // Chuyển đổi data request thành format phù hợp cho MongoDB
        const updatedData = data.map(item => {
          let objectUpdate = {
            unit: new mongoose.Types.ObjectId(item.unit),
            requiredOfficer: item.requiredOfficer,
            startTime: item.startTime,
            endTime: item.endTime,
            status: item.status
          };
          if(!item.status) {
            objectUpdate.endTime = 34144563600000
          }
          return objectUpdate;
        });

        updatedTemplate[index] = {
          ...currentItem,
          data: updatedData
        };

        // Cập nhật vào database
        DutyEmergencyScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: Date.now(),
              weeklySchedule: weeklyScheduleUpdate
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, {
            code: CONSTANTS.CODE.SUCCESS,
            message: {
              head: 'Thông báo',
              body: 'Cập nhật lịch trực đột xuất thành công'
            },
            data: updatedSchedule
          });
        });
      });
  };

  async.waterfall([checkParams, validateMaxOfficer, updateWeeklyScheduleTemplate], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
