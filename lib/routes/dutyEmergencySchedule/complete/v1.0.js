const _ = require('lodash');
const async = require('async');
const mongoose = require('mongoose');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DutyEmergencyScheduleModel = require('../../../models/dutyEmergencySchedule');
const DutyShiftModel = require('../../../models/dutyShift');
const UserModel = require('../../../models/user');
const UnitModel = require('../../../models/unit');
const PushNotifyManager = require('../../../jobs/pushNotify');

module.exports = (req, res) => {
  const userId = _.get(req, 'user.id');
  const { _id, templateId, templateShiftIds, shiftIds } = req.body;

  const checkParams = (next) => {
    if (!userId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    if (!_id) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID lịch trực đột xuất'
        }
      });
    }

    if (!mongoose.Types.ObjectId.isValid(_id)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID lịch trực đột xuất không hợp lệ'
        }
      });
    }

    if (!templateId) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp ID của phần tử template'
        }
      });
    }

    if (!mongoose.Types.ObjectId.isValid(templateId)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'ID phần tử template không hợp lệ'
        }
      });
    }

    if (!templateShiftIds || !Array.isArray(templateShiftIds)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp danh sách ID các ca trực template'
        }
      });
    }

    // Validate từng templateShiftId
    for (let i = 0; i < templateShiftIds.length; i++) {
      if (!mongoose.Types.ObjectId.isValid(templateShiftIds[i])) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `ID ca trực template thứ ${i + 1} không hợp lệ`
          }
        });
      }
    }

    if (!shiftIds || !Array.isArray(shiftIds)) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: {
          head: 'Thông báo',
          body: 'Vui lòng cung cấp danh sách ID các ca trực'
        }
      });
    }

    // Validate từng shiftId
    for (let i = 0; i < shiftIds.length; i++) {
      if (!mongoose.Types.ObjectId.isValid(shiftIds[i])) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: `ID ca trực thứ ${i + 1} không hợp lệ`
          }
        });
      }
    }

    next();
  };

  const updateTemplateShift = (next) => {
    // B1: Lấy lịch trực đột xuất và cập nhật ca trực trong template
    DutyEmergencyScheduleModel.findOne({ _id: _id, status: 1 })
      .lean()
      .exec((err, schedule) => {
        if (err) {
          return next(err);
        }

        if (!schedule) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Lịch trực đột xuất không tồn tại hoặc đã bị xóa'
            }
          });
        }

        // Tìm template theo templateId
        const templateIndex = schedule.weeklyScheduleTemplate.findIndex(
          item => item._id.toString() === templateId
        );

        if (templateIndex === -1) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không tìm thấy template ngày trực'
            }
          });
        }

        const template = schedule.weeklyScheduleTemplate[templateIndex];
        const currentTime = Date.now();
        let updatedTemplate = [...schedule.weeklyScheduleTemplate];
        let hasValidShifts = false;

        // Xử lý tất cả các ca trực template trong mảng templateShiftIds
        for (const templateShiftId of templateShiftIds) {
          // Tìm ca trực template theo templateShiftId
          const shiftIndex = template.data.findIndex(
            shift => shift._id.toString() === templateShiftId
          );

          if (shiftIndex === -1) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: `Không tìm thấy ca trực template với ID: ${templateShiftId}`
              }
            });
          }

          const currentShift = template.data[shiftIndex];

          // Kiểm tra endTime phải lớn hơn startTime
          if (currentTime <= currentShift.startTime) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: 'Thời gian kết thúc phải lớn hơn thời gian bắt đầu ca trực'
              }
            });
          }

          // Kiểm tra ca trực đã kết thúc chưa
          if (currentShift.status === 1) {
            return next({
              code: CONSTANTS.CODE.WRONG_PARAMS,
              message: {
                head: 'Thông báo',
                body: `Ca trực với ID ${templateShiftId} đã được kết thúc trước đó`
              }
            });
          }

          // Cập nhật template shift
          updatedTemplate[templateIndex].data[shiftIndex] = {
            ...currentShift,
            endTime: currentTime,
            status: 1
          };

          hasValidShifts = true;
        }

        if (!hasValidShifts) {
          return next({
            code: CONSTANTS.CODE.WRONG_PARAMS,
            message: {
              head: 'Thông báo',
              body: 'Không có ca trực template nào hợp lệ để cập nhật'
            }
          });
        }

        // Cập nhật vào database
        DutyEmergencyScheduleModel.findOneAndUpdate(
          { _id: _id, status: 1 },
          { 
            $set: { 
              weeklyScheduleTemplate: updatedTemplate,
              updatedAt: currentTime
            }
          },
          { new: true }
        )
        .lean()
        .exec((err, updatedSchedule) => {
          if (err) {
            return next(err);
          }

          next(null, { updatedSchedule, currentTime });
        });
      });
  };

  const updateDutyShifts = (data, next) => {
    // B2: Cập nhật các ca trực thực tế
    const { currentTime } = data;

    // Nếu không có shiftIds thì bỏ qua bước cập nhật DutyShiftModel
    if (!shiftIds || shiftIds.length === 0) {
      return next(null, {
        code: CONSTANTS.CODE.SUCCESS,
        message: {
          head: 'Thông báo',
          body: 'Kết thúc ca trực thành công'
        },
        data: {
          schedule: data.updatedSchedule,
          updatedShiftsCount: 0,
          endTime: currentTime
        },
        shifts: [] // Không có ca trực nào được cập nhật
      });
    }

    // Kiểm tra các ca trực có tồn tại không
    DutyShiftModel.find({ 
      _id: { $in: shiftIds.map(id => new mongoose.Types.ObjectId(id)) },
      statusEmmergency: { $ne: 1 } // Chưa kết thúc
    })
    .lean()
    .exec((err, shifts) => {
      if (err) {
        return next(err);
      }

      if (!shifts.length) {
        return next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Kết thúc ca trực thành công'
          },
          data: {
            schedule: data.updatedSchedule,
            updatedShiftsCount: 0,
            endTime: currentTime
          },
          shifts: [] // Không có ca trực nào được cập nhật
        });
      }

      // Kiểm tra endTime phải lớn hơn startTime cho tất cả ca trực
      const invalidShifts = shifts.filter(shift => currentTime <= shift.startTime);
      if (invalidShifts.length > 0) {
        return next({
          code: CONSTANTS.CODE.WRONG_PARAMS,
          message: {
            head: 'Thông báo',
            body: 'Thời gian kết thúc phải lớn hơn thời gian bắt đầu cho tất cả các ca trực'
          }
        });
      }

      // Cập nhật endTime cho các ca trực
      DutyShiftModel.updateMany(
        { 
          _id: { $in: shiftIds.map(id => new mongoose.Types.ObjectId(id)) }
        },
        { 
          $set: { 
            endTime: currentTime,
            statusEmmergency: 1,
            updatedAt: currentTime
          }
        }
      )
      .exec((err, updateResult) => {
        if (err) {
          return next(err);
        }

        next(null, {
          code: CONSTANTS.CODE.SUCCESS,
          message: {
            head: 'Thông báo',
            body: 'Kết thúc ca trực thành công'
          },
          data: {
            schedule: data.updatedSchedule,
            updatedShiftsCount: updateResult.modifiedCount,
            endTime: currentTime
          },
          shifts: shifts // Truyền thông tin ca trực để dùng trong pushNotify
        });
      });
    });
  };

  const pushNotify = (result, next) => {
    // Nếu không có ca trực nào được cập nhật, bỏ qua việc gửi thông báo
    if (!result.shifts || result.shifts.length === 0) {
      return next(null, result);
    }

    // Lấy danh sách officer từ các ca trực
    const officerIds = result.shifts.map(shift => shift.officer.toString()).filter((id, index, arr) => arr.indexOf(id) === index);
    
    if (officerIds.length === 0) {
      return next(null, result);
    }

    const title = 'Thông báo kết thúc ca trực';
    const description = 'Ca trực đột xuất của bạn đã được kết thúc';
    const notifyData = {
      link: 'MainContainer',
      extras: {
        tabIndex: 1
      },
      linkWeb: `/my-work-schedule`
    };

    const promises = officerIds.map(officerId => {
      return PushNotifyManager.sendToMember(officerId, title, description, notifyData, 'job_update', 'ioc');
    });
    
    Promise.all(promises)
      .then((results) => {
        next(null, {
          ...result,
          notificationResults: results
        });
      })
      .catch(err => {
        // Vẫn trả về kết quả thành công ngay cả khi gửi thông báo thất bại
        console.error('Error sending push notifications:', err);
        next(null, result);
      });
  };

  async.waterfall([checkParams, updateTemplateShift, updateDutyShifts, pushNotify], (err, data) => {
    err &&
      _.isError(err) &&
      (data = {
        code: CONSTANTS.CODE.SYSTEM_ERROR,
        message: MESSAGES.SYSTEM.ERROR,
      });

    res.json(data || err);
  });
};
