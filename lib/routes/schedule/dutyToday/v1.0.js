const _ = require('lodash');
const async = require('async');
const Joi = require('joi');

// Tái sử dụng service mới và các utils hiện có
const dutyTodayService = require('../../../services/dutyTodayService');
const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const DateUtils = require('../../../utils/dateUtils');

/**
 * API lấy danh sách lịch trực và lịch nghỉ của tất cả cán bộ trong ngày hiện tại
 * POST /api/v1.0/admin/schedule/duty-today
 *
 * Dành cho chỉ huy xem tổng quan lịch trực và lịch nghỉ hôm nay
 * Hỗ trợ sắp xếp, lọc, tìm kiếm và phân trang
 *
 * @param {string} sortBy - Sắp xếp theo: 'name', 'unit' - t<PERSON><PERSON> chọn, mặc định 'name'
 * @param {string} sortOrder - <PERSON><PERSON><PERSON> tự: 'asc', 'desc' - tùy chọn, mặc định 'asc'
 * @param {Object} filters - Bộ lọc: unit, dutyType, officer, textSearch - tùy chọn
 * @param {number} page - Trang hiện tại - tùy chọn, mặc định 1
 * @param {number} limit - Số bản ghi mỗi trang - tùy chọn, mặc định 20
 * @param {string} date - Ngày truy vấn (DD-MM-YYYY) - tùy chọn, mặc định là ngày hiện tại
 */
module.exports = (req, res) => {
  const {
    sortBy = 'name',
    sortOrder = 'asc',
    filters = {},
    page = 1,
    limit = 20,
    date = DateUtils.getCurrentDateDDMMYYYY()
  } = req.body;

  let result;

  const validateParams = (next) => {
    const schema = Joi.object({
      // Tham số sắp xếp
      sortBy: Joi.string().valid('name', 'unit').optional(),
      sortOrder: Joi.string().valid('asc', 'desc').optional(),

      // Bộ lọc
      filters: Joi.object({
        unit: Joi.string().optional(), // ObjectId của tổ
        dutyType: Joi.string().valid(
          'specialized', 'criminal', 'main', 'sub',
          'location', 'patrol', 'stadium', 'emergency'
        ).optional(),
        officer: Joi.string().optional(), // ObjectId của cán bộ
        textSearch: Joi.string().optional(), // Tìm kiếm theo tên cán bộ
      }).optional(),

      // Phân trang
      page: Joi.number().integer().min(1).optional(),
      limit: Joi.number().integer().min(1).max(100).optional(),

      // Ngày truy vấn
      date: Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).optional()
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  const getDutySchedulesToday = (next) => {
    try {
      const params = {
        sortBy,
        sortOrder,
        filters,
        page: parseInt(page),
        limit: parseInt(limit),
        date
      };

      dutyTodayService.getAllDutyShiftsToday(params)
        .then((response) => {
          if (!response || !response.success) {
            return next({
              code: CONSTANTS.CODE.SYSTEM_ERROR,
              message: response.message || MESSAGES.SYSTEM.ERROR
            });
          }

          result = response;
          next();
        })
        .catch((err) => {
          next(err);
        });
    } catch (error) {
      next(error);
    }
  };

  const formatResponse = (next) => {
    const { data } = result;

    // Format response theo chuẩn của hệ thống
    // Tái sử dụng cấu trúc response từ API hiện có
    const response = {
      code: CONSTANTS.CODE.SUCCESS,
      data
    };

    next(null, response);
  };

  // Tái sử dụng cấu trúc async.waterfall từ API hiện có
  async.waterfall([
    validateParams,
    getDutySchedulesToday,
    formatResponse
  ], (err, data) => {
    // Tái sử dụng error handling từ API hiện có
    if (_.isError(err)) {
      logger && logger.logError && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    })

    res.json(data || err)
  });
};