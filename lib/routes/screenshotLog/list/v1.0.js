const _ = require('lodash');
const async = require('async');
const Joi = require('joi');
const moment = require('moment');

const CONSTANTS = require('../../../const');
const MESSAGES = require('../../../message');
const ScreenshotLogModel = require('../../../models/screenshotLog');

/**
 * API lấy danh sách log chụp màn hình
 * POST /api/v1.0/screenshot-log/list
 *
 * L<PERSON>y danh sách log chụp màn hình theo các tiêu chí lọc
 */
module.exports = (req, res) => {
  const {
    startDate,
    endDate,
    page = 1,
    limit = 20,
    userId: targetUserId
  } = req.body;

  let totalCount = 0;

  /**
   * Validate tham số đầu vào
   */
  const validateParams = (next) => {
    const schema = Joi.object({
      startDate: Joi.date().iso(),
      endDate: Joi.date().iso().min(Joi.ref('startDate')),
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      userId: Joi.string().regex(/^[0-9a-fA-F]{24}$/)
    });

    const { error } = schema.validate(req.body, { allowUnknown: true });
    if (error) {
      return next({
        code: CONSTANTS.CODE.WRONG_PARAMS,
        message: MESSAGES.SYSTEM.WRONG_PARAMS
      });
    }

    next();
  };

  /**
   * Lấy danh sách log chụp màn hình
   */
  const getScreenshotLogs = (next) => {
    try {
      // Xây dựng query
      const query = {};

      // Lọc theo người dùng nếu có
      if (targetUserId) {
        query.user = targetUserId;
      }

      // Lọc theo khoảng thời gian nếu có
      if (startDate || endDate) {
        query.timestamp = {};

        if (startDate) {
          query.timestamp.$gte = moment(startDate).startOf('day').valueOf();
        }

        if (endDate) {
          query.timestamp.$lte = moment(endDate).endOf('day').valueOf();
        }
      }

      // Đếm tổng số bản ghi
      ScreenshotLogModel.countDocuments(query, (err, count) => {
        if (err) {
          console.error('Error counting screenshot logs:', err);
          return next({
            code: CONSTANTS.CODE.SYSTEM_ERROR,
            message: MESSAGES.SYSTEM.SYSTEM_ERROR
          });
        }

        totalCount = count;

        // Lấy danh sách log theo phân trang
        ScreenshotLogModel.find(query)
          .sort({ timestamp: -1 })
          .skip((page - 1) * limit)
          .limit(limit)
          .populate('user', 'name avatar idNumber phones')
          .exec((err, results) => {
            if (err) {
              console.error('Error fetching screenshot logs:', err);
              return next({
                code: CONSTANTS.CODE.SYSTEM_ERROR,
                message: MESSAGES.SYSTEM.SYSTEM_ERROR
              });
            }

            next({
              code: CONSTANTS.CODE.SUCCESS,
              data: {
                logs: results,
                pagination: {
                  total: totalCount,
                  page,
                  limit,
                  pages: Math.ceil(totalCount / limit)
                }
              }
            });
          });
      });
    } catch (error) {
      console.error('Exception fetching screenshot logs:', error);
      return next(error);
    }
  };

  // Thực thi các bước xử lý
  async.waterfall([
    validateParams,
    getScreenshotLogs
  ], (err) => {
    if (_.isError(err)) {
      logger && logger.logError([err], req.originalUrl, req.body);
      MailUtil && MailUtil.sendMail(`${req.originalUrl} - ${err} - ${JSON.stringify(req.body)}`);
    }

    // Xử lý lỗi hệ thống
    err && _.isError(err) && (data = {
      code: CONSTANTS.CODE.SYSTEM_ERROR,
      message: MESSAGES.SYSTEM.ERROR
    });

    res.json(data || err);
  });
};
