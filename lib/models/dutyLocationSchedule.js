const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');

const DutyLocationSchedule = new mongoose.Schema({
  name: {
    type: String,
    required: true // Tên template lịch trực 
  },
  description: {
    type: String // <PERSON>ô tả template
  },
  startTime: {
    type: Number,
    required: true // <PERSON><PERSON>y bắt đầu áp dụng
  },
  endTime: {
    type: Number,
    required: true // Ng<PERSON>y kết thúc áp dụng
  },
  dutyType: {
    type: String,
    required: true // Loại nhiệm vụ trực
  },
  dutyName: {
    type: String,
    required: true // Tên nhiệm vụ trực
  },
  location: {
    type: String // Địa điểm trực
  },
  savedNotification: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SavedNotification'
  },
  weeklyScheduleTemplate: [
    {
      name: String, // Tên của ngày trong tuần
      dayStartTime: {
        type: Number, // Thời gian bắt đầ<PERSON> ng<PERSON> (timestamp)
        required: true // B<PERSON>t buộc phải có
      },
      date: String, // Ngày/tháng để hiển thị (ví dụ: "1/9")
      data: [{
        unit: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'Unit' // Tham chiếu đến mô hình Unit
        },
        startTime: {
          type: Number
        },
        endTime: {
          type: Number
        },
        hasEquipment: {
          type: Boolean,
          default: false 
        },
        unitSub: {
          type: String // Tổ phụ trách
        }
      }]
    }
  ],
  // Lịch trực thực tế
  weeklySchedule: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyShift' // Tham chiếu đến mô hình DutyShift
  }],
  
  status: {
    type: Number,
    default: 1 // 1: active, 0: inactive
  },
  // Thông tin tạo và cập nhật
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('DutyLocationSchedule', DutyLocationSchedule);
