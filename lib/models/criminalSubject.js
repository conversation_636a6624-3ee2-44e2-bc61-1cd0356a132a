const mongoose = require('mongoose');
const mongoConnections = require('../connections/mongo');
const Schema = mongoose.Schema;

const CriminalSubjectSchema = new mongoose.Schema(
  {
    // Thông tin định danh
    name: {
      type: String,
      required: true,
      trim: true
    },
    nameAlias: {
      type: String, // Tên được chuẩn hóa để tìm kiếm
      index: true
    },
    photos: [{
      type: String // Array URLs ảnh chân dung
    }],
    dob: {
      type: String, // Ngày sinh DD/MM/YYYY
      validate: {
        validator: function(v) {
          if (!v) return true; // Cho phép empty
          return /^\d{2}\/\d{2}\/\d{4}$/.test(v);
        },
        message: 'Ngày sinh phải có định dạng DD/MM/YYYY'
      }
    },
    gender: {
      type: String,
      enum: ['male', 'female']
    },
    idNumber: {
      type: String, // CMND/CCCD
      sparse: true, // <PERSON> phép null nhưng unique nếu có giá trị
      index: true
    },
    phones: [{
      type: String // Danh sách số điện thoại
    }],

    // Thông tin cư trú
    permanentAddress: {
      type: String // Địa chỉ thường trú
    },
    temporaryAddress: {
      type: String // Địa chỉ tạm trú
    },
    currentResidence: {
      type: String // Chỗ ở hiện tại
    },

    // Phân loại pháp lý
    category: {
      type: String,
      required: true,
      enum: ['security', 'criminal', 'drug', 'other']
    },
    dangerLevel: {
      type: String,
      required: true,
      enum: ['low', 'medium', 'high']
    },
    legalStatus: {
      type: String, // Tình trạng pháp lý
      required: true,
      enum: ['pretrial', 'trial', 'suspended_sentence', 'detention', 'arrest', 'free'] // tiền án, tiền sự, án treo, quản chế, truy nã, tự do
    },

    // Mô tả đối tượng
    description: {
      type: String // Hành vi/thói quen, mối quan hệ xã hội, dấu hiệu bất thường
    },

    // Thông tin quản lý
    managingUnit: {
      type: Schema.Types.ObjectId,
      ref: 'Unit'
    },
    assignedOfficers: [{
      type: Schema.Types.ObjectId,
      ref: 'User' // Mảng cán bộ phụ trách
    }],
    businessNotes: {
      type: String // Ghi chú nghiệp vụ
    },
    areas: [{
      type: String,  // Mảng khu vực địa bàn
      ref: 'Area'
    }],

    // Audit
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    createdAt: {
      type: Number,
      default: Date.now,
      index: true
    },
    updatedAt: {
      type: Number,
      default: Date.now
    },
    deletedAt: {
      type: Number
    },
    status: {
      type: Number,
      default: 1, // 1: active, 0: inactive
      index: true
    }
  },
  { id: false, versionKey: false }
);

// Indexes for search optimization
CriminalSubjectSchema.index({ nameAlias: 1, status: 1 });
CriminalSubjectSchema.index({ idNumber: 1, status: 1 });
CriminalSubjectSchema.index({ phones: 1, status: 1 });
CriminalSubjectSchema.index({ category: 1, status: 1 });
CriminalSubjectSchema.index({ dangerLevel: 1, status: 1 });
CriminalSubjectSchema.index({ managingUnit: 1, status: 1 });
CriminalSubjectSchema.index({ assignedOfficers: 1, status: 1 });
CriminalSubjectSchema.index({ createdAt: -1, status: 1 });

// Compound index for text search
CriminalSubjectSchema.index({
  nameAlias: 'text',
  idNumber: 'text',
  phones: 'text'
}, {
  weights: {
    nameAlias: 10,
    idNumber: 5,
    phones: 3
  }
});

module.exports = mongoConnections('master').model('CriminalSubject', CriminalSubjectSchema);
