const mongoose = require('mongoose');
const _ = require('lodash')
const mongoConnections = require('../connections/mongo')
const UserModel = require('./user');

const DutyShift = new mongoose.Schema({
  startTime: {
    type: Number,
    required: true
  },
  endTime: {
    type: Number,
    required: true
  },
  officer: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'User'
  },
  status:{
    type: Number,
    default: 0 // 0: chưa xác nhận, 1: đã xác nhận, 2: hủy bỏ
  },
  statusEmmergency: {
    type: Number,
    default: 0 // 0: đang trực, 1: đã kết thúc
  },
  name: {
    type: String,
    required: true
  },
  forLeader: {
    type: Boolean,
    default: false // true: ca trực dành cho lãnh đạo, false: ca trực bình thường
  },
  locationDuty: {
    type: String, // Đ<PERSON>a điểm cụ thể trực
  },
  unit: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Unit'
  },
  description: {
    type: String // Mô tả nhiệm vụ ca trực
  },
  assignedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: {
    type: String // Ghi chú
  },
  hasEquipment: {
    type: Boolean
  },
  dutySpecializedSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutySpecializedSchedule'
  },
  dutyCriminalSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyCriminalSchedule'
  },
  dutyMainSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyMainSchedule'
  },
  dutySubSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutySubSchedule'
  },
  dutyLocationSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyLocationSchedule'
  },
  dutyPatrolSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyPatrolSchedule'
  },
  dutyStadiumSchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyStadiumSchedule'
  },
  dutyEmergencySchedule: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DutyEmergencySchedule'
  },
  isCriminal: {
    type: Boolean
  },
  source: {
    type: String, // Nguồn gốc ca trực (ví dụ: từ lịch trực chuyên trách)
    default: 'manual' // 'manual': tạo thủ công, 'template': từ mẫu lịch trực
  },
  createdAt: {
    type: Number,
    default: Date.now
  },
  updatedAt: {
    type: Number,
    default: Date.now
  }
}, { id: false, versionKey: false });

module.exports = mongoConnections('master').model('DutyShift', DutyShift);
