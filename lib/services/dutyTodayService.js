const _ = require('lodash');

// Models
const DutyShift = require('../models/dutyShift');
const LeaveRequest = require('../models/leaveRequest');
const User = require('../models/user');

// Utils
const statisticsUtils = require('../utils/statisticsUtils');
const { change_alias } = require('../util/tool');

/**
 * Service lấy danh sách lịch trực và lịch nghỉ của tất cả cán bộ trong ngày hiện tại
 * Dành cho chỉ huy xem tổng quan lịch trực và lịch nghỉ hôm nay
 */
class DutyTodayService {

  /**
   * L<PERSON>y danh sách lịch trực và lịch nghỉ hôm nay của tất cả cán bộ
   * @param {Object} params - Tham số filter, sort, pagination
   * @returns {Object} Kết quả với phân trang
   */
  async getAllDutyShiftsToday(params = {}) {
    try {
      const { filters = {}, sortBy = 'name', sortOrder = 'asc', page = 1, limit = 20, date } = params;

      // Tính toán khoảng thời gian hôm nay
      const todayRange = statisticsUtils.getTimeRange('custom', date, date);

      // Lấy danh sách user IDs từ textSearch nếu có
      let searchUserIds = null;
      if (filters.textSearch && filters.textSearch.trim()) {
        searchUserIds = await this.getUserIdsByTextSearch(filters.textSearch.trim());
        if (!searchUserIds || searchUserIds.length === 0) {
          // Không tìm thấy user nào khớp với textSearch
          return {
            success: true,
            data: {
              dutySchedules: [],
              pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: 0,
                totalPages: 0
              },
              summary: {
                totalOfficersOnDuty: 0,
                totalDutyShifts: 0,
                totalOfficersOnLeave: 0,
                dutyTypes: {}
              }
            }
          };
        }
      }

      // Lấy tất cả ca trực hôm nay
      const dutyShifts = await this.fetchTodayDutyShifts(todayRange, filters, searchUserIds);

      // Lấy tất cả lịch nghỉ hôm nay
      const leaveRequests = await this.fetchTodayLeaveRequests(todayRange, searchUserIds);

      // Nhóm theo cán bộ
      const groupedByOfficer = this.groupDutyShiftsByOfficer(dutyShifts);

      // Thêm thông tin lịch nghỉ vào dữ liệu cán bộ
      const officersWithLeave = await this.mergeLeaveInfoToOfficers(groupedByOfficer, leaveRequests);

      // Áp dụng sắp xếp
      const sortedOfficers = await this.applySorting(officersWithLeave, sortBy, sortOrder);

      // Áp dụng phân trang
      const paginatedResult = this.applyPagination(sortedOfficers, page, limit);

      // Tạo thống kê tổng quan
      const summary = this.generateSummary(dutyShifts, leaveRequests);

      return {
        success: true,
        data: {
          pagination: paginatedResult.pagination,
          summary,
          dutySchedules: paginatedResult.data
        }
      };

    } catch (error) {
      console.error('Error in getAllDutyShiftsToday:', error);
      return {
        success: false
      };
    }
  }

  /**
   * Lấy tất cả ca trực hôm nay từ database
   * Tái sử dụng logic query từ scheduleAggregationService.getDutyShifts()
   */
  async fetchTodayDutyShifts(todayRange, filters, searchUserIds = null) {
    const query = {
      // Lấy ca trực có giao với ngày hôm nay (bao gồm ca qua đêm)
      status: 1,
      $or: [
        // Ca bắt đầu hôm nay
        {
          startTime: { $gte: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
        },
        // Ca kết thúc hôm nay
        {
          endTime: { $gt: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
        },
        // Ca bao trùm cả ngày hôm nay
        {
          startTime: { $lte: todayRange.startTimestamp },
          endTime: { $gte: todayRange.endTimestamp }
        }
      ]
    };

    // Áp dụng filters
    this.applyFilters(query, filters);

    // Áp dụng textSearch filter nếu có
    if (searchUserIds && searchUserIds.length > 0) {
      query.officer = { $in: searchUserIds };
    }

    const shifts = await DutyShift.find(query)
      .populate({
        path: 'officer',
        select: 'name idNumber avatar positions units managedUnits phones',
        populate: [{
          path: 'units',
          select: 'name parentPath'
        }, {
          path: 'positions',
          select: 'name'
        }, {
          path: 'managedUnits',
          select: 'name parentPath'
        }]
      })
      .lean();

    return shifts;
  }

  /**
   * Áp dụng các bộ lọc vào query
   */
  applyFilters(query, filters) {
    // Lọc theo tổ
    if (filters.unit) {
      query.unit = filters.unit;
    }

    // Lọc theo cán bộ cụ thể
    if (filters.officer) {
      query.officer = filters.officer;
    }

    // Lọc theo loại lịch trực
    if (filters.dutyType) {
      const dutyTypeFilters = [];

      // Mapping các loại lịch trực
      const dutyTypeMapping = {
        'specialized': 'dutySpecializedSchedule',
        'criminal': 'dutyCriminalSchedule',
        'main': 'dutyMainSchedule',
        'sub': 'dutySubSchedule',
        'location': 'dutyLocationSchedule',
        'patrol': 'dutyPatrolSchedule',
        'stadium': 'dutyStadiumSchedule',
        'emergency': 'dutyEmergencySchedule'
      };

      if (dutyTypeMapping[filters.dutyType]) {
        const condition = {};
        condition[dutyTypeMapping[filters.dutyType]] = { $exists: true, $ne: null };
        dutyTypeFilters.push(condition);
      }

      if (dutyTypeFilters.length > 0) {
        query.$and = query.$and || [];
        query.$and.push({ $or: dutyTypeFilters });
      }
    }
  }

  /**
   * Nhóm ca trực theo cán bộ
   * Tái sử dụng logic groupBy từ lodash như trong scheduleAggregationService
   */
  groupDutyShiftsByOfficer(dutyShifts) {
    const grouped = _.groupBy(dutyShifts, shift => shift.officer._id.toString());

    return Object.keys(grouped).map(officerId => {
      const shifts = grouped[officerId];
      const officer = shifts[0].officer; // Lấy thông tin officer từ shift đầu tiên

      return {
        officer: officer,
        dutyShifts: shifts.map(shift => ({
          _id: shift._id,
          title: shift.name,
          startTime: shift.startTime,
          endTime: shift.endTime === 34144563600000 ? null : shift.endTime,
          location: shift.locationDuty,
          status: shift.status,
          forLeader: shift.forLeader,
          description: shift.description,
          notes: shift.notes,
          hasEquipment: shift.hasEquipment,
          type: this.getDutyType(shift),
          createdAt: shift.createdAt
        }))
      };
    });
  }

  /**
   * Xác định loại lịch trực từ shift
   */
  getDutyType(shift) {
    if (shift.dutySpecializedSchedule) return 'specialized';
    if (shift.dutyCriminalSchedule) return 'criminal';
    if (shift.dutyMainSchedule) return 'main';
    if (shift.dutySubSchedule) return 'sub';
    if (shift.dutyLocationSchedule) return 'location';
    if (shift.dutyPatrolSchedule) return 'patrol';
    if (shift.dutyStadiumSchedule) return 'stadium';
    if (shift.dutyEmergencySchedule) return 'emergency';
    return 'manual';
  }

  /**
   * Áp dụng sắp xếp
   * Tái sử dụng logic sort từ lodash
   */
  async applySorting(groupedOfficers, sortBy, sortOrder) {
    const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';

    return _.orderBy(groupedOfficers, [
      (item) => {
        switch (sortBy) {
          case 'name':
            // Sắp xếp theo tên: lấy từ cuối cùng (họ tên Việt Nam)
            const getLastName = (fullName) => {
              if (!fullName) return '';
              const nameParts = fullName.trim().split(' ');
              return nameParts[nameParts.length - 1];
            };

            return getLastName(item.officer.name);
          case 'unit':
            // Lấy tên tổ từ ca trực đầu tiên
            return item.officer?.units[item.officer?.units.length - 1]?.name || '';
          default:
            return item.officer.name;
        }
      }
    ], [sortDirection]);
  }

  /**
   * Áp dụng phân trang
   * Tái sử dụng logic pagination chuẩn
   */
  applyPagination(data, page, limit) {
    const totalRecords = data.length;
    const totalPages = Math.ceil(totalRecords / limit);
    const offset = (page - 1) * limit;
    const paginatedData = data.slice(offset, offset + limit);

    return {
      data: paginatedData,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalRecords,
        limit: parseInt(limit),
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Tạo thống kê tổng quan
   * Tái sử dụng logic từ scheduleAggregationService.generateSummary()
   */
  generateSummary(dutyShifts, leaveRequests = []) {
    const uniqueOfficers = new Set();
    const uniqueOfficersOnLeave = new Set();
    const dutyTypes = {};

    dutyShifts.forEach(shift => {
      // Đếm số cán bộ duy nhất
      uniqueOfficers.add(shift.officer._id.toString());

      // Đếm theo loại lịch trực
      const dutyType = this.getDutyType(shift);
      dutyTypes[dutyType] = (dutyTypes[dutyType] || 0) + 1;
    });

    leaveRequests.forEach(leave => {
      uniqueOfficersOnLeave.add(leave.user._id.toString());
    });

    return {
      totalOfficersOnDuty: uniqueOfficers.size,
      totalDutyShifts: dutyShifts.length,
      totalOfficersOnLeave: uniqueOfficersOnLeave.size,
      dutyTypes
    };
  }

  /**
   * Lấy danh sách user IDs từ textSearch
   * @param {string} textSearch - Chuỗi tìm kiếm
   * @returns {Array} Danh sách user IDs
   */
  async getUserIdsByTextSearch(textSearch) {
    try {
      const nameAliasSearch = change_alias(textSearch);

      const users = await User.find({
        $or: [
          { phones: textSearch },
          { idNumber: textSearch },
          { nameAlias: new RegExp(nameAliasSearch, 'i') }
        ]
      }).select('_id').lean();

      return users.map(user => user._id);
    } catch (error) {
      console.error('Error in getUserIdsByTextSearch:', error);
      return [];
    }
  }

  /**
   * Lấy tất cả lịch nghỉ hôm nay từ database
   * @param {Object} todayRange - Khoảng thời gian hôm nay
   * @param {Array} searchUserIds - Danh sách user IDs từ textSearch
   * @returns {Array} Danh sách lịch nghỉ
   */
  async fetchTodayLeaveRequests(todayRange, searchUserIds = null) {
    try {
      const query = {
        status: 'approved', // Chỉ lấy lịch nghỉ đã được duyệt
        // Lấy lịch nghỉ có giao với ngày hôm nay
        $or: [
          // Lịch nghỉ bắt đầu hôm nay
          {
            startTime: { $gte: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
          },
          // Lịch nghỉ kết thúc hôm nay
          {
            endTime: { $gt: todayRange.startTimestamp, $lte: todayRange.endTimestamp }
          },
          // Lịch nghỉ bao trùm cả ngày hôm nay
          {
            startTime: { $lte: todayRange.startTimestamp },
            endTime: { $gte: todayRange.endTimestamp }
          }
        ]
      };

      // Áp dụng textSearch filter nếu có
      if (searchUserIds && searchUserIds.length > 0) {
        query.user = { $in: searchUserIds };
      }

      const leaveRequests = await LeaveRequest.find(query)
        .populate({
          path: 'user',
          select: 'name idNumber avatar positions units managedUnits',
          populate: [{
            path: 'units',
            select: 'name parentPath'
          }, {
            path: 'positions',
            select: 'name'
          }, {
            path: 'managedUnits',
            select: 'name parentPath'
          }]
        })
        .lean();

      return leaveRequests;
    } catch (error) {
      console.error('Error in fetchTodayLeaveRequests:', error);
      return [];
    }
  }

  /**
   * Merge thông tin lịch nghỉ vào dữ liệu cán bộ
   * @param {Array} groupedByOfficer - Danh sách cán bộ đã nhóm theo ca trực
   * @param {Array} leaveRequests - Danh sách lịch nghỉ
   * @returns {Array} Danh sách cán bộ đã merge thông tin lịch nghỉ
   */
  async mergeLeaveInfoToOfficers(groupedByOfficer, leaveRequests) {
    try {
      // Tạo map lịch nghỉ theo user ID (chỉ lấy lịch nghỉ đầu tiên)
      const leaveMap = {};
      leaveRequests.forEach(leave => {
        const userId = leave.user._id.toString();
        if (!leaveMap[userId]) {
          // Chỉ lấy lịch nghỉ đầu tiên cho mỗi user
          leaveMap[userId] = {
            type: leave.type,
            startDate: leave.startDate,
            endDate: leave.endDate,
            reason: leave.reason
          };
        }
      });

      // Thêm thông tin lịch nghỉ vào từng cán bộ
      const result = groupedByOfficer.map(officerData => {
        const officerId = officerData.officer._id.toString();
        const leaveInfo = leaveMap[officerId] || null;

        return {
          ...officerData,
          leaveInfo
        };
      });

      // Thêm các cán bộ chỉ có lịch nghỉ mà không có lịch trực
      const officerIdsWithDuty = new Set(groupedByOfficer.map(o => o.officer._id.toString()));

      for (const leave of leaveRequests) {
        const userId = leave.user._id.toString();
        if (!officerIdsWithDuty.has(userId)) {
          // Cán bộ này chỉ có lịch nghỉ, không có lịch trực
          result.push({
            officer: leave.user,
            dutyShifts: [],
            leaveInfo: leaveMap[userId] || null
          });
          officerIdsWithDuty.add(userId);
        }
      }

      return result;
    } catch (error) {
      console.error('Error in mergeLeaveInfoToOfficers:', error);
      return groupedByOfficer;
    }
  }
}

module.exports = new DutyTodayService();