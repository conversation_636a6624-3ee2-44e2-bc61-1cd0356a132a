const express = require('express');
const cors = require('cors');

// Global variables
global._ = require('lodash');
global.config = require('config');
global.Logger = require('./lib/logger');
global.mongoose = require('mongoose');
global.fs = require('fs');
global.moment = require('moment');
global.async = require('async');
global.ms = require('ms');
global.MailUtil = require('./lib/util/mail');
global.logger = Logger(`${__dirname}/logs`);

// Load models
fs.readdirSync(`${__dirname}/lib/models`).forEach((file) => {
  global[_.upperFirst(_.camelCase(file.replace('.js', 'Model')))] = require(`./lib/models/${file}`);
});

// Middleware
const bodyParser = require('body-parser');
const tokenToUserMiddleware = require('./lib/middleware/tokenToUser');
const verifyTokenMiddleware = require('./lib/middleware/verifyToken');
const verifyPermission = require('./lib/middleware/verifyPermission');

// Socket.IO
const socketManager = require('./lib/socket/socketManager');

// Handle routes
const UserHandle = require('./lib/routes/user');
const UserAdminHandle = require('./lib/routes/admin/user');
const PermissionAdminHandle = require('./lib/routes/admin/permission');
const GroupPermissionAdminHandle = require('./lib/routes/admin/groupPermission');
const CategoryPermissionAdminHandle = require('./lib/routes/admin/categoryPermission');
const UnitAdminHandle = require('./lib/routes/admin/unit');
const RankAdminHandle = require('./lib/routes/admin/rank');
const PositionAdminHandle = require('./lib/routes/admin/position');
const AreaAdminHandle = require('./lib/routes/admin/area');
const HandbookCategoryAdminHandle = require('./lib/routes/admin/handbookCategory');
const HandbookAdminHandle = require('./lib/routes/admin/handbook');
const JobTypeAdminHandle = require('./lib/routes/admin/jobType');
const DutyShiftTemplateHandle = require('./lib/routes/dutyShiftTemplate');
const DutySpecializedScheduleHandle = require('./lib/routes/dutySpecializedSchedule');
const DutyShiftHandle = require('./lib/routes/dutyShift');
const DutyCriminalScheduleHandle = require('./lib/routes/dutyCriminalSchedule');
const DutyMainScheduleHandle = require('./lib/routes/dutyMainSchedule');
const DutySubScheduleHandle = require('./lib/routes/dutySubSchedule');
const ReportHandle = require('./lib/routes/report');
const DutyLocationScheduleHandle = require('./lib/routes/dutyLocationSchedule');
const DutyPatrolScheduleHandle = require('./lib/routes/dutyPatrolSchedule');
const DutyStadiumScheduleHandle = require('./lib/routes/dutyStadiumSchedule');
const DutyEmergencyScheduleHandle = require('./lib/routes/dutyEmergencySchedule');
const NotifyHandle = require('./lib/routes/notify');
const MeetingScheduleHandle = require('./lib/routes/meetingSchedule');
const AdminMissionHandle = require('./lib/routes/admin/mission');
const MissionHandle = require('./lib/routes/mission');

// Attendance system handles
const WorkScheduleHandle = require('./lib/routes/workSchedule');
const WorkScheduleAdminHandle = require('./lib/routes/admin/workSchedule');
const AttendanceHandle = require('./lib/routes/attendance');
const AttendanceAdminHandle = require('./lib/routes/admin/attendance');
const SuddenAttendanceHandle = require('./lib/routes/suddenAttendance');
const SuddenAttendanceAdminHandle = require('./lib/routes/admin/suddenAttendance');
const LeaveRequestHandle = require('./lib/routes/leaveRequest');
const HandbookHandle = require('./lib/routes/handbook');

// Statistics system handles
const StatisticsHandle = require('./lib/routes/statistics');
const AIStatisticsHandle = require('./lib/routes/ai-statistic');

// Admin notification handling
const NotifyAdminHandle = require('./lib/routes/admin/notify');

// 🚀 NEW: Debouncer admin handling
const DebouncerAdminHandle = require('./lib/routes/admin/debouncer');

// Schedule aggregation system
const ScheduleHandle = require('./lib/routes/schedule');

// Statistics detail routes
const DetailReportSummaryHandle = require('./lib/routes/statisticsDetail/reportsSummary');
const DetailReportAreaHandle = require('./lib/routes/statisticsDetail/reportsByArea');
const DetailReportCategoryHandle = require('./lib/routes/statisticsDetail/reportsByCategory');
const DetailAttendanceHandle = require('./lib/routes/statisticsDetail/attendance');

const LeaveRequestAdminHandle = require('./lib/routes/admin/leaveRequest');

// Screenshot Log routes
const ScreenshotLogHandle = require('./lib/routes/screenshotLog');

// Criminal Subject routes
const CriminalSubjectHandle = require('./lib/routes/criminalSubject');
const CriminalSubjectUpdateHandle = require('./lib/routes/criminalSubjectUpdate');

// Start server
const app = express();
app.set('trust proxy', true);
const server = require('http').Server(app);

// Initialize Socket.IO with our custom manager
socketManager.initialize(server);
global.io = socketManager.io; // For backward compatibility

// Middleware setup
app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(express.static('public'));

// Define route declaration function
const declareRoute = (method, routeName, middlewares = [], destinationRoute) => {
  if (!destinationRoute || !routeName) {
    return;
  }

  Object.keys(destinationRoute).forEach((version) => {
    app[method](`/api/${version}${routeName}`, middlewares, destinationRoute[version]);
  });
};

// API Routes - Example routes for the template
declareRoute('post', '/user/login', [], UserHandle.login);
declareRoute('post', '/user/logout', [tokenToUserMiddleware], UserHandle.logout);
declareRoute('post', '/user/get', [tokenToUserMiddleware], UserHandle.get);
declareRoute('post', '/user/change-password', [tokenToUserMiddleware], UserHandle.changePassword);
declareRoute('post', '/user/send-otp', [], UserHandle.sendOTP);
declareRoute('post', '/user/change-password-otp', [], UserHandle.checkOTPChangePassword);

declareRoute('post', '/admin/user/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-tai-khoan')], UserAdminHandle.list);
declareRoute('post', '/admin/user/create', [tokenToUserMiddleware, verifyPermission('create-user')], UserAdminHandle.create);
declareRoute('post', '/admin/user/update', [tokenToUserMiddleware, verifyPermission('update-user')], UserAdminHandle.update);
declareRoute('post', '/admin/user/inactive', [tokenToUserMiddleware, verifyPermission('inactive-user')], UserAdminHandle.inactive);
declareRoute('post', '/admin/user/get', [tokenToUserMiddleware, verifyPermission('get-user')], UserAdminHandle.get);
declareRoute('post', '/admin/user/grant-permission', [tokenToUserMiddleware, verifyPermission('grant-pemission')], UserAdminHandle.grantPermission);
declareRoute('post', '/admin/user/toggle-block', [tokenToUserMiddleware, verifyPermission('update-user')], UserAdminHandle.toggleBlock);

declareRoute('post', '/admin/permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.list);
declareRoute('post', '/admin/permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.create);
declareRoute('post', '/admin/permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.update);
declareRoute('post', '/admin/permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.inactive);
declareRoute('post', '/admin/permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], PermissionAdminHandle.get);

declareRoute('post', '/admin/group-permission/list', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.list);
declareRoute('post', '/admin/group-permission/create', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.create);
declareRoute('post', '/admin/group-permission/update', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.update);
declareRoute('post', '/admin/group-permission/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.inactive);
declareRoute('post', '/admin/group-permission/get', [tokenToUserMiddleware, verifyPermission('quan-ly-quyen-han')], GroupPermissionAdminHandle.get);

declareRoute('post', '/admin/unit/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.list);
declareRoute('post', '/admin/unit/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.get);
declareRoute('post', '/admin/unit/list-level', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], UnitAdminHandle.listUnitLevel);
declareRoute('post', '/admin/unit/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], UnitAdminHandle.create);
declareRoute('post', '/admin/unit/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.update);
declareRoute('post', '/admin/unit/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], UnitAdminHandle.inactive);
declareRoute('post', '/admin/unit/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], UnitAdminHandle.ordering);

declareRoute('post', '/admin/position/list', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.list);
declareRoute('post', '/admin/position/get', [tokenToUserMiddleware, verifyPermission('xem-danh-sach-don-vi')], PositionAdminHandle.get);
declareRoute('post', '/admin/position/create', [tokenToUserMiddleware, verifyPermission('taodonvi')], PositionAdminHandle.create);
declareRoute('post', '/admin/position/update', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.update);
declareRoute('post', '/admin/position/inactive', [tokenToUserMiddleware, verifyPermission('xoa-don-vi')], PositionAdminHandle.inactive);
declareRoute('post', '/admin/position/ordering', [tokenToUserMiddleware, verifyPermission('sua-don-vi')], PositionAdminHandle.ordering);

declareRoute('post', '/admin/category-permission/list', [tokenToUserMiddleware], CategoryPermissionAdminHandle.list);

declareRoute('post', '/admin/area/list', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.list);
declareRoute('post', '/admin/area/get', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.get);
declareRoute('post', '/admin/area/create', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.create);
declareRoute('post', '/admin/area/update', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.update);
declareRoute('post', '/admin/area/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-khu-vuc')], AreaAdminHandle.inactive);

declareRoute('post', '/admin/job-type/list', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.list);
declareRoute('post', '/admin/job-type/get', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.get);
declareRoute('post', '/admin/job-type/create', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.create);
declareRoute('post', '/admin/job-type/update', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.update);
declareRoute('post', '/admin/job-type/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-loai-cong-viec')], JobTypeAdminHandle.inactive);


// Duty Shift Template Routes
declareRoute('post', '/duty-shift-template/create', [tokenToUserMiddleware, verifyPermission('create-duty-shift-template')], DutyShiftTemplateHandle.create);
declareRoute('post', '/duty-shift-template/update', [tokenToUserMiddleware, verifyPermission('update-duty-shift-template')], DutyShiftTemplateHandle.update);
declareRoute('post', '/duty-shift-template/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-shift-template')], DutyShiftTemplateHandle.inactive);
declareRoute('post', '/duty-shift-template/get', [tokenToUserMiddleware, verifyPermission('get-duty-shift-template')], DutyShiftTemplateHandle.get);

// Duty Specialized Schedule Routes
declareRoute('post', '/duty-specialized-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-specialized-schedule')], DutySpecializedScheduleHandle.get);
declareRoute('post', '/duty-specialized-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-specialized-schedule')], DutySpecializedScheduleHandle.updateShift);
declareRoute('post', '/duty-specialized-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-specialized-schedule')], DutySpecializedScheduleHandle.inactive);
declareRoute('post', '/duty-specialized-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-specialized-schedule')], DutySpecializedScheduleHandle.updateTemplate);

// Duty Shift Routes
declareRoute('post', '/duty-shift/create', [tokenToUserMiddleware, verifyPermission('create-duty-shift')], DutyShiftHandle.create);
declareRoute('post', '/duty-shift/update', [tokenToUserMiddleware, verifyPermission('update-duty-shift')], DutyShiftHandle.update);
declareRoute('post', '/duty-shift/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-shift')], DutyShiftHandle.inactive);
declareRoute('post', '/duty-shift/list', [tokenToUserMiddleware, verifyPermission('list-duty-shift')], DutyShiftHandle.list);
declareRoute('post', '/duty-shift/notify-all', [tokenToUserMiddleware, verifyPermission('notify-all-duty-shift')], DutyShiftHandle.notifyAll);

// Duty Criminal Schedule Routes
declareRoute('post', '/duty-criminal-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-criminal-schedule')], DutyCriminalScheduleHandle.get);
declareRoute('post', '/duty-criminal-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-criminal-schedule')], DutyCriminalScheduleHandle.updateShift);
declareRoute('post', '/duty-criminal-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-criminal-schedule')], DutyCriminalScheduleHandle.updateTemplate);
declareRoute('post', '/duty-criminal-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-criminal-schedule')], DutyCriminalScheduleHandle.inactive);
declareRoute('post', '/duty-criminal-schedule/copy-template', [tokenToUserMiddleware, verifyPermission('copy-template-duty-criminal-schedule')], DutyCriminalScheduleHandle.copyTemplate);

// Duty Main Schedule Routes
declareRoute('post', '/duty-main-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-main-schedule')], DutyMainScheduleHandle.get);
declareRoute('post', '/duty-main-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-main-schedule')], DutyMainScheduleHandle.updateShift);
declareRoute('post', '/duty-main-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-main-schedule')], DutyMainScheduleHandle.inactive);

// Duty Sub Schedule Routes
declareRoute('post', '/duty-sub-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-sub-schedule')], DutySubScheduleHandle.get);
declareRoute('post', '/duty-sub-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-sub-schedule')], DutySubScheduleHandle.updateShift);
declareRoute('post', '/duty-sub-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-sub-schedule')], DutySubScheduleHandle.inactive);

// Report Routes
declareRoute('post', '/report/create', [tokenToUserMiddleware], ReportHandle.create);
declareRoute('post', '/report/update', [tokenToUserMiddleware], ReportHandle.update);
declareRoute('post', '/report/list', [tokenToUserMiddleware], ReportHandle.list);
declareRoute('post', '/report/statistics', [tokenToUserMiddleware], ReportHandle.statistics);
declareRoute('post', '/report/templates', [tokenToUserMiddleware], ReportHandle.templates);
declareRoute('post', '/report/get', [tokenToUserMiddleware], ReportHandle.get);

// Duty Location Schedule Routes
declareRoute('post', '/duty-location-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-location-schedule')], DutyLocationScheduleHandle.get);
declareRoute('post', '/duty-location-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-location-schedule')], DutyLocationScheduleHandle.updateShift);
declareRoute('post', '/duty-location-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-location-schedule')], DutyLocationScheduleHandle.inactive);
declareRoute('post', '/duty-location-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-location-schedule')], DutyLocationScheduleHandle.updateTemplate);
declareRoute('post', '/duty-location-schedule/list-location', [tokenToUserMiddleware, verifyPermission('list-duty-location')], DutyLocationScheduleHandle.listLocation);

// Duty Patrol Schedule Routes
declareRoute('post', '/duty-patrol-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-patrol-schedule')], DutyPatrolScheduleHandle.get);
declareRoute('post', '/duty-patrol-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-patrol-schedule')], DutyPatrolScheduleHandle.updateShift);
declareRoute('post', '/duty-patrol-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-patrol-schedule')], DutyPatrolScheduleHandle.inactive);
declareRoute('post', '/duty-patrol-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-patrol-schedule')], DutyPatrolScheduleHandle.updateTemplate);
// Duty Stadium Schedule Routes
declareRoute('post', '/duty-stadium-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-stadium-schedule')], DutyStadiumScheduleHandle.get);
declareRoute('post', '/duty-stadium-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-stadium-schedule')], DutyStadiumScheduleHandle.updateShift);
declareRoute('post', '/duty-stadium-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-stadium-schedule')], DutyStadiumScheduleHandle.inactive);
declareRoute('post', '/duty-stadium-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-stadium-schedule')], DutyStadiumScheduleHandle.updateTemplate);

// Duty Emergency Schedule Routes
declareRoute('post', '/duty-emergency-schedule/get', [tokenToUserMiddleware, verifyPermission('get-duty-emergency-schedule')], DutyEmergencyScheduleHandle.get);
declareRoute('post', '/duty-emergency-schedule/update-shift', [tokenToUserMiddleware, verifyPermission('update-shift-duty-emergency-schedule')], DutyEmergencyScheduleHandle.updateShift);
declareRoute('post', '/duty-emergency-schedule/inactive', [tokenToUserMiddleware, verifyPermission('inactive-duty-emergency-schedule')], DutyEmergencyScheduleHandle.inactive);
declareRoute('post', '/duty-emergency-schedule/update-template', [tokenToUserMiddleware, verifyPermission('update-template-duty-emergency-schedule')], DutyEmergencyScheduleHandle.updateTemplate);
declareRoute('post', '/duty-emergency-schedule/complete', [tokenToUserMiddleware, verifyPermission('complete-duty-emergency-schedule')], DutyEmergencyScheduleHandle.complete);

// Work Schedule Routes
declareRoute('post', '/admin/work-schedule/create', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.create);
declareRoute('post', '/admin/work-schedule/list', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.list);
declareRoute('post', '/admin/work-schedule/update', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.update);
declareRoute('post', '/admin/work-schedule/delete', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.delete);
declareRoute('post', '/admin/work-schedule/generate-weekly', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.generateWeekly);
declareRoute('post', '/admin/work-schedule/notify-all', [tokenToUserMiddleware, verifyPermission('tao-lich-lam-viec')], WorkScheduleAdminHandle.notifyAll);
declareRoute('post', '/work-schedule/get', [tokenToUserMiddleware], WorkScheduleHandle.get);

// Attendance Routes
declareRoute('post', '/attendance/checkin', [tokenToUserMiddleware], AttendanceHandle.checkin);
declareRoute('post', '/attendance/history', [tokenToUserMiddleware], AttendanceHandle.history);
declareRoute('post', '/attendance/status', [tokenToUserMiddleware], AttendanceHandle.status);
declareRoute('post', '/attendance/statistics', [tokenToUserMiddleware], AttendanceHandle.statistics);
declareRoute('post', '/attendance/status-overview', [tokenToUserMiddleware], AttendanceHandle.statusOverview);
declareRoute('post', '/admin/attendance/statistics', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], AttendanceAdminHandle.statistics);
declareRoute('post', '/admin/attendance/daily-statistics', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], AttendanceAdminHandle.dailyStatistics);

// Sudden Attendance Routes (User)
declareRoute('post', '/sudden-attendance/checkin', [tokenToUserMiddleware], SuddenAttendanceHandle.checkin);
declareRoute('post', '/sudden-attendance/list', [tokenToUserMiddleware], SuddenAttendanceHandle.list);

// Sudden Attendance Admin Routes
declareRoute('post', '/admin/sudden-attendance/create', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.create);
declareRoute('post', '/admin/sudden-attendance/list', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.list);
declareRoute('post', '/admin/sudden-attendance/get', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.get);
declareRoute('post', '/admin/sudden-attendance/update', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.update);
declareRoute('post', '/admin/sudden-attendance/delete', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.delete);
declareRoute('post', '/admin/sudden-attendance/statistics', [tokenToUserMiddleware, verifyPermission('quan-ly-diem-danh-dot-xuat')], SuddenAttendanceAdminHandle.statistics);

// Leave Request Routes
declareRoute('post', '/leave-request/create', [tokenToUserMiddleware], LeaveRequestHandle.create);
declareRoute('post', '/leave-request/list', [tokenToUserMiddleware], LeaveRequestHandle.list);
declareRoute('post', '/leave-request/detail', [tokenToUserMiddleware], LeaveRequestHandle.detail);
declareRoute('post', '/admin/leave-request/approve', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestHandle.approve);
declareRoute('post', '/admin/leave-request/create', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestAdminHandle.create);
declareRoute('post', '/admin/leave-request/update', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestAdminHandle.update);
declareRoute('post', '/admin/leave-request/delete', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestAdminHandle.delete);
declareRoute('post', '/admin/leave-request/list', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestAdminHandle.list);
declareRoute('post', '/admin/leave-request/get', [tokenToUserMiddleware, verifyPermission('quan-ly-lịch-nghi')], LeaveRequestAdminHandle.get);

// Statistics Routes
declareRoute('post', '/statistics/on-duty-officers', [tokenToUserMiddleware], StatisticsHandle.onDutyOfficers);
declareRoute('post', '/statistics/attendance', [tokenToUserMiddleware], StatisticsHandle.attendance);
declareRoute('post', '/statistics/late-attendance', [tokenToUserMiddleware], StatisticsHandle.attendance);
declareRoute('post', '/statistics/officer-summary', [tokenToUserMiddleware], StatisticsHandle.officerSummary);
declareRoute('post', '/statistics/officers-by-area', [tokenToUserMiddleware], StatisticsHandle.officersByArea);

// Reports Statistics Routes
declareRoute('post', '/statistics/reports-by-area', [tokenToUserMiddleware], StatisticsHandle.reportsByArea);
declareRoute('post', '/statistics/reports-summary', [tokenToUserMiddleware], StatisticsHandle.reportsSummary);
declareRoute('post', '/statistics/reports-incidents-highlight', [tokenToUserMiddleware], StatisticsHandle.reportsIncidentsHighlight);
declareRoute('post', '/statistics/reports-incidents-other', [tokenToUserMiddleware], StatisticsHandle.reportsIncidentsOther);
declareRoute('post', '/statistics/reports-status', [tokenToUserMiddleware], StatisticsHandle.reportsStatus);
declareRoute('post', '/statistics/latest-incidents', [tokenToUserMiddleware], StatisticsHandle.latestIncidents);

// Documents Statistics Routes
declareRoute('post', '/statistics/documents-summary', [tokenToUserMiddleware], StatisticsHandle.documentsSummary);

// Meeting Schedule Routes
declareRoute('post', '/meeting-schedule/create', [tokenToUserMiddleware, verifyPermission('tao-lich-hop')], MeetingScheduleHandle.create);
declareRoute('post', '/meeting-schedule/list', [tokenToUserMiddleware, verifyPermission('xem-lich-hop')], MeetingScheduleHandle.list);
declareRoute('post', '/meeting-schedule/get', [tokenToUserMiddleware, verifyPermission('xem-lich-hop')], MeetingScheduleHandle.get);
declareRoute('post', '/meeting-schedule/update', [tokenToUserMiddleware, verifyPermission('cap-nhat-lich-hop')], MeetingScheduleHandle.update);
declareRoute('post', '/meeting-schedule/inactive', [tokenToUserMiddleware, verifyPermission('xoa-lich-hop')], MeetingScheduleHandle.inactive);
declareRoute('post', '/meeting-schedule/list-for-officer', [tokenToUserMiddleware], MeetingScheduleHandle.listForOfficer);

// Notify Routes
declareRoute('post', '/notify/add-token', [], NotifyHandle.addToken);
declareRoute('post', '/notify/list', [tokenToUserMiddleware], NotifyHandle.list);
declareRoute('post', '/notify/get', [tokenToUserMiddleware], NotifyHandle.get);
declareRoute('post', '/notify/count-unread', [tokenToUserMiddleware], NotifyHandle.countUnreads);
declareRoute('post', '/notify/seen', [tokenToUserMiddleware], NotifyHandle.seen);
// Admin Notification Routes
declareRoute('post', '/admin/notify/create', [tokenToUserMiddleware, verifyPermission('create-notify')], NotifyAdminHandle.create);
declareRoute('post', '/admin/notify/list', [tokenToUserMiddleware, verifyPermission('list-notify')], NotifyAdminHandle.list);
declareRoute('post', '/admin/notify/get', [tokenToUserMiddleware, verifyPermission('get-notify')], NotifyAdminHandle.get);
declareRoute('post', '/admin/notify/update', [tokenToUserMiddleware, verifyPermission('update-notify')], NotifyAdminHandle.modify);
declareRoute('post', '/admin/notify/inactive', [tokenToUserMiddleware, verifyPermission('inactive-notify')], NotifyAdminHandle.inactive);
declareRoute('post', '/admin/notify/push-notify', [tokenToUserMiddleware, verifyPermission('push-notify')], NotifyAdminHandle.pushNotify);
declareRoute('post', '/admin/notify/test-notify', [tokenToUserMiddleware, verifyPermission('push-notify')], NotifyAdminHandle.testNotify);

// 🚀 NEW: Debouncer Admin Routes
declareRoute('get', '/admin/debouncer/status', [tokenToUserMiddleware, verifyPermission('admin-system')], DebouncerAdminHandle.status);
declareRoute('post', '/admin/debouncer/control', [tokenToUserMiddleware, verifyPermission('admin-system')], DebouncerAdminHandle.control);

// AI Statistics Routes
declareRoute('post', '/ai-statistic/report-security', [tokenToUserMiddleware], AIStatisticsHandle.reportSecurity);
declareRoute('post', '/ai-statistic/report-security-by-area', [tokenToUserMiddleware], AIStatisticsHandle.reportSecurityByArea);
declareRoute('post', '/ai-statistic/report-security-by-category', [tokenToUserMiddleware], AIStatisticsHandle.reportSecurityByCategory);
declareRoute('post', '/ai-statistic/report-support', [tokenToUserMiddleware], AIStatisticsHandle.reportSupport);
declareRoute('post', '/ai-statistic/jobs', [tokenToUserMiddleware], AIStatisticsHandle.jobs);

// Schedule Aggregation Routes
declareRoute('post', '/schedule/grouped', [tokenToUserMiddleware], ScheduleHandle.grouped);
declareRoute('post', '/admin/schedule/duty-today', [tokenToUserMiddleware, verifyPermission('quan-ly-lich-lam-viec')], ScheduleHandle.dutyToday);
declareRoute('post', '/admin/schedule/duty-officer', [tokenToUserMiddleware, verifyPermission('quan-ly-lich-lam-viec')], ScheduleHandle.dutyOfficer);

// Admin Rank Routes
declareRoute('post', '/admin/rank/list', [tokenToUserMiddleware], RankAdminHandle.list);

// Admin Statistics Detail Routes
declareRoute('post', '/admin/detail-reports-summary/document', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.document);
declareRoute('post', '/admin/detail-reports-summary/identity', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.identity);
declareRoute('post', '/admin/detail-reports-summary/protection', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.protection);
declareRoute('post', '/admin/detail-reports-summary/license-plate', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.licensePlate);
declareRoute('post', '/admin/detail-reports-summary/security', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.security);
declareRoute('post', '/admin/detail-reports-summary/security-by-area', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportSummaryHandle.securityByArea);

// Admin Statistics Routes by Area
declareRoute('post', '/admin/reports-by-area/total', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportAreaHandle.total);
declareRoute('post', '/admin/reports-by-area/category', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportAreaHandle.category);

// Admin Statistics Routes by Category
declareRoute('post', '/admin/reports-by-category/total', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportCategoryHandle.total);
declareRoute('post', '/admin/reports-by-category/area', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportCategoryHandle.area);
declareRoute('post', '/admin/reports-by-category/list-category', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-bao-cao')], DetailReportCategoryHandle.listCategory);

// Statistics Detail Routes
declareRoute('post', '/admin/attendance/total', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], DetailAttendanceHandle.total);
declareRoute('post', '/admin/attendance/statistic', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], DetailAttendanceHandle.statistic);
declareRoute('post', '/admin/attendance/report', [tokenToUserMiddleware, verifyPermission('xem-thong-ke-diem-danh')], DetailAttendanceHandle.report);

// Admin Handbook Category Routes
declareRoute('post', '/admin/handbook-category/list', [tokenToUserMiddleware, verifyPermission('quan-ly-danh-muc-sach-bao-cao')], HandbookCategoryAdminHandle.list);
declareRoute('post', '/admin/handbook-category/get', [tokenToUserMiddleware, verifyPermission('quan-ly-danh-muc-sach-bao-cao')], HandbookCategoryAdminHandle.get);
declareRoute('post', '/admin/handbook-category/create', [tokenToUserMiddleware, verifyPermission('quan-ly-danh-muc-sach-bao-cao')], HandbookCategoryAdminHandle.create);
declareRoute('post', '/admin/handbook-category/update', [tokenToUserMiddleware, verifyPermission('quan-ly-danh-muc-sach-bao-cao')], HandbookCategoryAdminHandle.update);
declareRoute('post', '/admin/handbook-category/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-danh-muc-sach-bao-cao')], HandbookCategoryAdminHandle.inactive);

// Admin Handbook Routes
declareRoute('post', '/admin/handbook/list', [tokenToUserMiddleware, verifyPermission('quan-ly-sach-bao-cao')], HandbookAdminHandle.list);
declareRoute('post', '/admin/handbook/get', [tokenToUserMiddleware, verifyPermission('quan-ly-sach-bao-cao')], HandbookAdminHandle.get);
declareRoute('post', '/admin/handbook/create', [tokenToUserMiddleware, verifyPermission('quan-ly-sach-bao-cao')], HandbookAdminHandle.create);
declareRoute('post', '/admin/handbook/update', [tokenToUserMiddleware, verifyPermission('quan-ly-sach-bao-cao')], HandbookAdminHandle.update);
declareRoute('post', '/admin/handbook/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-sach-bao-cao')], HandbookAdminHandle.inactive);

// Handbook Routes
declareRoute('post', '/handbook/list-category', [tokenToUserMiddleware], HandbookHandle.listCategory);
declareRoute('post', '/handbook/list', [tokenToUserMiddleware], HandbookHandle.list);
declareRoute('post', '/handbook/get', [tokenToUserMiddleware], HandbookHandle.get);
declareRoute('post', '/handbook/seen', [tokenToUserMiddleware], HandbookHandle.seen);

// Screenshot Log Routes
declareRoute('post', '/screenshot-log/create', [tokenToUserMiddleware], ScreenshotLogHandle.create);
declareRoute('post', '/screenshot-log/list', [tokenToUserMiddleware], ScreenshotLogHandle.list);
// Mission Routes
declareRoute('post', '/admin/mission/create', [tokenToUserMiddleware, verifyPermission('tao-nhiem-vu')], AdminMissionHandle.create);
declareRoute('post', '/admin/mission/update', [tokenToUserMiddleware, verifyPermission('cap-nhat-nhiem-vu')], AdminMissionHandle.update);
declareRoute('post', '/admin/mission/list', [tokenToUserMiddleware, verifyPermission('xem-nhiem-vu')], AdminMissionHandle.list);
declareRoute('post', '/admin/mission/get', [tokenToUserMiddleware, verifyPermission('xem-nhiem-vu')], AdminMissionHandle.get);
declareRoute('post', '/admin/mission/cancel', [tokenToUserMiddleware, verifyPermission('huy-nhiem-vu')], AdminMissionHandle.cancel);
declareRoute('post', '/admin/mission/assign-officer', [tokenToUserMiddleware, verifyPermission('giao-nhiem-vu')], AdminMissionHandle.assignOfficer);
declareRoute('post', '/admin/mission/update-number-of-user', [tokenToUserMiddleware, verifyPermission('giao-nhiem-vu')], AdminMissionHandle.updateNumberOfUser);
declareRoute('post', '/admin/mission/complete', [tokenToUserMiddleware, verifyPermission('hoan-thanh-nhiem-vu')], AdminMissionHandle.complete);
declareRoute('post', '/admin/mission/start', [tokenToUserMiddleware, verifyPermission('bat-dau-nhiem-vu')], AdminMissionHandle.start);
declareRoute('post', '/admin/mission-log/list', [tokenToUserMiddleware, verifyPermission('xem-nhiem-vu')], AdminMissionHandle.getLogs);
declareRoute('post', '/admin/mission/statistic', [tokenToUserMiddleware, verifyPermission('thong-ke-nhiem-vu')], AdminMissionHandle.statistic);
declareRoute('post', '/admin/mission/ai-statistic', [tokenToUserMiddleware, verifyPermission('thong-ke-nhiem-vu')], AdminMissionHandle.aiStatistic);
declareRoute('post', '/admin/mission/report', [tokenToUserMiddleware, verifyPermission('thong-ke-nhiem-vu')], AdminMissionHandle.report);
declareRoute('post', '/admin/mission/notify', [tokenToUserMiddleware, verifyPermission('thong-bao-nhiem-vu')], AdminMissionHandle.notify);
declareRoute('post', '/mission/accept', [tokenToUserMiddleware], MissionHandle.accept);
declareRoute('post', '/mission/reject', [tokenToUserMiddleware], MissionHandle.reject);

// Criminal Subject Admin Routes
declareRoute('post', '/criminal-subject/create', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectHandle.create);
declareRoute('post', '/criminal-subject/list', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectHandle.list);
declareRoute('post', '/criminal-subject/get', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectHandle.get);
declareRoute('post', '/criminal-subject/update', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectHandle.update);
declareRoute('post', '/criminal-subject/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectHandle.inactive);
declareRoute('post', '/criminal-subject/statistic-summary', [tokenToUserMiddleware, verifyPermission('thong-ke-doi-tuong-hinh-su')], CriminalSubjectHandle.statisticSummary);
declareRoute('post', '/criminal-subject/ai-statistic-summary', [tokenToUserMiddleware, verifyPermission('thong-ke-doi-tuong-hinh-su')], CriminalSubjectHandle.aiStatisticSummary);
declareRoute('post', '/criminal-subject/statistic-subject', [tokenToUserMiddleware, verifyPermission('thong-ke-doi-tuong-hinh-su')], CriminalSubjectHandle.statisticSubject);
declareRoute('post', '/criminal-subject/ai-statistic-subject', [tokenToUserMiddleware, verifyPermission('thong-ke-doi-tuong-hinh-su')], CriminalSubjectHandle.aiStatisticSubject);
declareRoute('post', '/criminal-subject/report', [tokenToUserMiddleware, verifyPermission('thong-ke-doi-tuong-hinh-su')], CriminalSubjectHandle.report);

// Criminal Subject Update Routes
declareRoute('post', '/criminal-subject-update/create', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectUpdateHandle.create);
declareRoute('post', '/criminal-subject-update/list', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectUpdateHandle.list);
declareRoute('post', '/criminal-subject-update/get', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectUpdateHandle.get);
declareRoute('post', '/criminal-subject-update/update', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectUpdateHandle.update);
declareRoute('post', '/criminal-subject-update/inactive', [tokenToUserMiddleware, verifyPermission('quan-ly-doi-tuong-hinh-su')], CriminalSubjectUpdateHandle.inactive);

app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date() });
});

// Start attendance notification job
const attendanceNotificationJob = require('./lib/jobs/attendanceNotificationJob');
attendanceNotificationJob.init(); // Run at specific times: 7:30, 7:55, 8:00, 13:30, 13:55, 14:00
// attendanceNotificationJob.testReminder('afternoon', 5); // Run at specific times: 7:30, 7:55, 8:00, 13:30, 13:55, 14:00
app.post('/test-reminder', (req, res) => {
  attendanceNotificationJob.testReminder(req.body.shift, req.body.minutesBefore);
  res.json({ code: 200, timestamp: new Date() });
});

// Start weekly schedule job
const weeklyScheduleJob = require('./lib/jobs/weeklyScheduleJob');
weeklyScheduleJob.start(); // Run at 22:00 every Friday

// Start statistics metadata job
const statisticsMetadataJob = require('./lib/jobs/statisticsMetadataJob');
statisticsMetadataJob.start(); // Run metadata calculation every 10 minutes, trigger processing every minute

// 🚀 NEW: Initialize Statistics Debouncer System
const { initializeDebouncer } = require('./lib/startup/debouncerStartup');
initializeDebouncer();

// Start attendance status sync job
const AttendanceStatusSyncJobStartup = require('./lib/startup/attendanceStatusSyncJobStartup');
AttendanceStatusSyncJobStartup.init(); // Run at 12:00 and 18:00 daily

// Start sudden attendance job
const SuddenAttendanceJob = require('./lib/jobs/suddenAttendanceJob');
SuddenAttendanceJob.init(); // Run every minute for status updates and every 5 minutes for reminders

// Start meeting reminder job
const MeetingManager = require('./lib/jobs/meetingManager');
MeetingManager.startReminderJob(); // Check for meeting reminders every 5 minutes

const port = _.get(config, 'port', 3000);
server.listen(port, () => {
  logger.logInfo('Server listening at port:', port);
});

process.on('uncaughtException', (err) => {
  logger.logError('uncaughtException', err);
});
process.on('unhandledRejection', (reason, promise) => {
  logger.logError('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.logInfo('SIGTERM received, shutting down gracefully...');
  AttendanceStatusSyncJobStartup.shutdown();
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.logInfo('SIGINT received, shutting down gracefully...');
  AttendanceStatusSyncJobStartup.shutdown();
  process.exit(0);
});
